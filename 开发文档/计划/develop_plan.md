# “忆帧定制”前端开发计划

**文档目标**: 本计划旨在将 `AstroWind` 模板逐步改造为完全符合 `GEMINI.md` 准则的 "忆帧定制 (MemoFrame Studio)" 官方网站。计划将分阶段进行，确保每一步都有明确的目标和可交付的成果。

---

## 阶段一：基础设置与品牌化 (Foundation & Branding)

**目标**: 剥离模板的通用特征，注入 "忆帧定制" 的核心视觉识别和品牌基调。

### 任务 1.1: 项目清理与初始化

-   **更新 `README.md`**: 删除 `AstroWind` 的所有介绍，替换为 "忆帧定制" 项目的简介、技术栈和启动说明。
-   **更新网站配置**:
    -   修改 `/src/config.yaml` 文件，将 `site.name` 和 `metadata` 等字段更新为 "忆帧定制" 的信息。这是全站 SEO 和元数据的核心。
-   **清理模板文件**:
    -   删除或清空 `/src/content/post/` 目录下的所有示例博客文章。
    -   检查 `/public/` 目录，删除与 `AstroWind` 相关的示例图片或文件。

### 任务 1.2: 视觉识别 (Visual Identity)

-   **定义品牌色板**: 根据 `GEMINI.md` 中“共情、温暖、富有艺术感”的基调，确定品牌的主色、辅色和强调色。
-   **更新颜色配置**:
    -   找到定义 CSS 变量的文件 (通常在 `src/assets/styles/` 目录下)。
    -   修改 `--aw-color-primary`, `--aw-color-secondary`, `--aw-color-accent` 等变量的值为你定义的品牌色。
-   **定义品牌字体**:
    -   选择符合品牌调性的标题字体和正文字体（例如，艺术感的衬线字体用于标题，清晰的无衬线字体用于正文）。
    -   在 `tailwind.config.js` 中，修改 `theme.extend.fontFamily` 部分，引入并应用新字体。
-   **替换 Logo 和 Favicon**:
    -   将 "忆帧定制" 的 Logo 文件放入 `src/assets/images/`。
    -   修改 `src/components/Logo.astro` 或 `src/components/widgets/Header.astro`，使其引用新的 Logo。
    -   将全套 Favicon 文件替换 `src/assets/favicons/` 目录下的文件。

---

## 阶段二：核心内容与组件适配

**目标**: 将网站的核心页面内容替换为业务所需，并调整组件以符合用户旅程。

### 任务 2.1: 核心页面内容填充

-   **首页 (`src/pages/index.astro`)**:
    -   修改 `Hero` 部分的文案，使用 Slogan：“你的故事，独一无二的艺术品。”
    -   将主要的 CTA (Call to Action) 按钮文案改为 **“开始我的定制”**。
    -   暂时用静态内容填充“核心产品线”和“服务流程”等板块。
-   **关于我们 (`src/pages/about.astro`)**: 撰写品牌故事和理念。
-   **联系方式 (`src/pages/contact.astro`)**: 提供清晰的联系方式，特别是微信二维码。

### 任务 2.2: 组件适配与改造

-   **导航栏与页脚 (`src/components/widgets/Header.astro`, `Footer.astro`)**:
    -   更新导航链接，确保其指向正确的页面（如：首页、作品集、服务介绍、关于我们、联系我们）。
    -   在页脚添加版权信息、社交媒体链接等。
-   **作品集组件**:
    -   这是网站的**核心**。需要设计和开发一个专门的**作品集**展示组件 (`src/components/PortfolioGrid.astro`)。
    -   初期可以使用静态数据和本地图片进行填充，为后续接入 Strapi 做准备。

---

## 阶段三：Strapi CMS 集成 (Headless Architecture)

**目标**: 实现前后端分离，让网站内容可以通过 Strapi 后台进行动态管理。这是技术改造的核心。

### 任务 3.1: Strapi 后端建模

-   **安装与配置 Strapi**: 搭建一个本地或云端的 Strapi 服务。
-   **创建内容类型 (Content-Types)**:
    -   **作品 (Portfolio)**: 字段包括 `标题`, `描述`, `封面图`, `图片集`, `视频链接`, `类别` (动画/漫画等)。
    -   **博客文章 (Post)**: 字段包括 `标题`, `内容 (Rich Text)`, `封面图`, `分类`, `标签`。
    -   **服务项目 (Service)**: 字段包括 `服务名称`, `详细介绍`, `价格范围`, `示例图片`。
-   **配置 API 权限**:
    -   为所有需要前端展示的内容类型开启 `find` 和 `findOne` 的公共访问权限。
    -   在 Strapi 后台生成一个**只读 (Read-only)** 的 API Token。

### 任务 3.2: 前端数据对接

-   **配置环境变量**:
    -   创建 `.env` 文件 (已在 `.gitignore` 中忽略)。
    -   根据 `.env.example`，填入你的 Strapi 服务 URL 和上一步生成的 API Token。
    -   `STRAPI_URL=http://127.0.0.1:1337`
    -   `STRAPI_API_TOKEN=your_api_token_here`
-   **创建数据获取工具**:
    -   在 `src/utils/` 目录下，创建 `strapi.ts` 文件，用于封装 `fetch` 请求，统一处理与 Strapi 的通信。
    -   为从 Strapi 获取的数据定义清晰的 TypeScript 接口（类型），例如 `PortfolioItem`, `Post` 等。

### 任务 3.3: 页面动态化

-   **改造作品集页面**:
    -   修改 `src/pages/portfolio/index.astro`，在页面加载时从 Strapi 获取所有作品数据，并传递给作品集组件进行渲染。
    -   创建动态路由页面 `src/pages/portfolio/[slug].astro`，用于展示单个作品的详情。
-   **改造博客页面**:
    -   修改 `src/pages/[...blog]/` 下的页面，将数据源从本地 Markdown 文件改为从 Strapi 获取。
-   **改造首页**:
    -   让首页的“精选作品”和“服务项目”等板块也从 Strapi 动态获取数据。

---

## 阶段四：功能完善与交付准备

**目标**: 完成核心业务流程的闭环，并进行最终的优化和测试。

### 任务 4.1: “开始我的定制”流程

-   **引导式表单**:
    -   设计并实现一个引导式的需求提交表单。
    -   **技术选型**: 此处是应用 Vue.js 的绝佳场景，可以利用其强大的交互能力构建一个用户友好的表单组件。
    -   在 Astro 页面中，通过 `client:load` 指令加载这个 Vue 组件。
-   **表单提交**:
    -   将表单数据提交到后端服务（可以使用 Strapi 的自定义 API，或 Vercel/Netlify 的 Serverless Functions）。

---

## 阶段五：创意优化与视觉增强

**目标**: 在核心功能完成后，为网站注入更多创造性的视觉元素和细节打磨，提升品牌感和用户体验。

### 任务 5.1: 页面细节微调

-   **内容与样式优化**:
    -   逐一审查核心页面的文案和视觉元素，进行精细化调整。
    -   根据品牌语调，优化按钮文本、标题和段落的措辞。
    -   微调颜色、边距和字体，提升整体视觉和谐度。

### 任务 5.2: 引入 3D 视觉效果

-   **技术探索与实现**:
    -   **技术选型**: 确定使用 TresJS (Vue + Three.js) 作为 3D 实现方案。
    -   **创意实现**: 实现“记忆放映机”或“灵感粒子汇聚”等创意 3D 交互。
    -   在关键区域（如首页 Hero）引入 3D 模型或动画，创造令人印象深刻的视觉焦点。

---

## 阶段六：最终测试与部署

**目标**: 完成最终的质量保证流程，并正式将网站上线。

### 任务 6.1: 综合测试与优化

-   **全面检查 SEO**: 确保所有页面都有动态生成的、独特的 `title` 和 `description`。
-   **无障碍访问 (a11y)**: 系统性检查全站的语义化标签、`alt` 文本、键盘导航和色彩对比度。
-   **性能优化**: 重点关注 3D 元素对性能的影响，确保不影响核心 Web 指标。

### 任务 6.2: 部署

-   **预发布**: 在 Vercel 或 Netlify 上部署预发布环境进行最终验收。
-   **生产部署**: 将前端项目和 Strapi 后端正式部署到生产环境。

---

**开发者**: `gtken991`
**当前版本**: v1.0.0-beta (基于 AstroWind)
**目标版本**: v1.1.0 (忆帧定制-视觉增强版)