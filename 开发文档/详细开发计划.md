# “忆帧定制”前端改造详细开发计划 (两周冲刺)

## 总体目标
在两周内，将 AstroWind 模板完全改造为符合 `GEMINI.md` 规范的“忆帧定制”官方网站，实现核心业务流程闭环，并准备好上线。

**开发者**: `gtken991`
**版本控制**: 所有开发工作在 `feature/` 分支上进行，完成后合并到 `develop` 分支。

---

## 第一周：品牌落地、静态内容与核心组件

### **Day 1: 项目启动与品牌注入**
- **核心目标**: 清理模板，注入品牌DNA，统一团队认知。
- **任务清单**:
    1.  **需求对齐**: 再次精读 `GEMINI.md`，确保对品牌语调、用户旅程和技术规范的理解完全一致。
    2.  **项目清理 (执行 `develop_plan.md` 任务 1.1)**:
        -   [ ] 更新 `README.md`，替换为“忆帧定制”项目信息。
        -   [ ] 删除 `/src/content/post/` 下的示例博客。
        -   [ ] 清理 `/public/` 目录下的模板示例文件。
    3.  **全局配置**:
        -   [ ] 修改 `src/config.ts` (或类似配置文件)，更新网站名称、元数据等信息为“忆帧定制”。
        -   [ ] 根据 `GEMINI.md` #5 的 SEO 策略，初步设定核心关键词。

### **Day 2: 视觉识别系统 (VI) 实施**
- **核心目标**: 将网站从“模板”外观转变为“品牌”外观。
- **任务清单 (执行 `develop_plan.md` 任务 1.2)**:
    1.  **品牌色板**:
        -   [ ] 在 `tailwind.config.cjs` 中，根据“共情、温暖、富有艺术感”的基调，定义并替换 `primary`, `secondary`, `accent` 等颜色。
    2.  **品牌字体**:
        -   [ ] 选择并引入指定的标题和正文字体。
        -   [ ] 在 `tailwind.config.cjs` 的 `theme.extend.fontFamily` 中应用新字体。
    3.  **Logo & Favicon**:
        -   [ ] 替换 `src/assets/images/` 中的 Logo 文件。
        -   [ ] 修改 `Header` 组件，使其引用新 Logo。
        -   [ ] 替换 `/public/favicons/` 目录下的所有图标文件。

### **Day 3: 核心布局与导航组件**
- **核心目标**: 搭建网站骨架，确保核心导航功能正确。
- **任务清单 (执行 `develop_plan.md` 任务 2.2 部分)**:
    1.  **布局调整**: 审查并调整 `src/layouts/Layout.astro`，确保其符合整体设计。
    2.  **导航栏 (`Header`) 改造**:
        -   [ ] 更新导航链接为：首页、作品集、服务介绍、关于我们、联系我们。
        -   [ ] 确保导航栏在移动端和桌面端都有良好的响应式表现。
    3.  **页脚 (`Footer`) 改造**:
        -   [ ] 添加“忆帧定制”的版权信息。
        -   [ ] 添加指向社交媒体（小红书/TikTok）的链接。
    4.  **国际化 (i18n) 基础**:
        -   [ ] 根据 `Astro国际化实践指南.md`，配置 `src/i18n/languages.ts` (添加 `zh-cn`) 和 `src/i18n/ui.ts`。
        -   [ ] 将 `Header` 和 `Footer` 中的静态文本（如导航链接、版权信息）替换为 `t('key')` 的形式。

### **Day 4: 首页内容填充与组装**
- **核心目标**: 完成首页的静态内容搭建，使其成为一个完整的品牌门面。
- **任务清单 (执行 `develop_plan.md` 任务 2.1 部分)**:
    1.  **Hero Section**:
        -   [ ] 修改 `Hero` 部分的文案，使用 Slogan：“你的故事，独一无二的艺术品。”
        -   [ ] 将主 CTA 按钮文案改为 **“开始我的定制”**，并暂时链接到 `#contact`。
    2.  **内容板块**:
        -   [ ] 使用静态内容创建“核心产品线”展示区。
        -   [ ] 使用静态内容创建“服务流程”或“如何开始”板块。
        -   [ ] 创建一个静态的“精选作品”区域作为占位符，为 Day 8 的动态数据接入做准备。
    3.  **图片资源**: 确保所有在首页使用的图片都通过 Astro 的 `<Image />` 组件加载，并提供有意义的 `alt` 文本。

### **Day 5: 静态页面与作品集组件**
- **核心目标**: 完成除首页外的核心静态页面，并开发关键的**作品集**组件。
- **任务清单**:
    1.  **页面开发**:
        -   [ ] 创建并填充 `src/pages/about.astro`（关于我们）页面内容。
        -   [ ] 创建并填充 `src/pages/contact.astro`（联系我们）页面，务必包含清晰的联系方式和微信二维码。
    2.  **作品集组件开发 (执行 `develop_plan.md` 任务 2.2 部分)**:
        -   [ ] 创建 `src/components/PortfolioGrid.astro` 组件。
        -   [ ] 设计卡片样式，用于展示单个作品的封面图和标题。
        -   [ ] 使用本地静态假数据（例如，一个JS数组）和本地图片填充网格，确保布局和样式正确。
    3.  **代码审查**: 对本周完成的所有组件和页面进行一次快速的代码审查。

---

## 第二周：动态化、核心功能与交付

### **Day 6: Strapi 后端建模**
- **核心目标**: 搭建内容管理后台，为网站动态化提供数据源。
- **任务清单 (执行 `develop_plan.md` 任务 3.1)**:
    1.  **Strapi 安装与配置**: 在本地或云端（如 Render）搭建 Strapi 服务。
    2.  **内容类型创建**:
        -   [ ] **作品 (Portfolio)**: 包含 `标题`, `描述`, `封面图`, `图片集`, `视频链接`, `类别`, `slug` 等字段。
        -   [ ] **服务项目 (Service)**: 包含 `服务名称`, `详细介绍`, `价格范围`, `示例图片` 等字段。
        -   [ ] **博客文章 (Post)**: 包含 `标题`, `内容 (Rich Text)`, `封面图`, `slug` 等字段。
        -   [ ] **(可选) 国际化支持**: 在创建内容类型时，为需要翻译的字段（如标题、描述）开启 Strapi 的 `localization` 功能。
    3.  **API 权限配置**:
        -   [ ] 为所有内容类型开启 `find` 和 `findOne` 的公共访问权限。
        -   [ ] 在 Strapi 后台生成一个**只读 (Read-only)** 的 API Token。

### **Day 7: 前端数据对接**
- **核心目标**: 将前端与 Strapi 连接，实现首页内容的动态化。
- **任务清单 (执行 `develop_plan.md` 任务 3.2 & 3.3 部分)**:
    1.  **环境配置**:
        -   [ ] 创建 `.env` 文件，并填入 `STRAPI_URL` 和 `STRAPI_API_TOKEN`。
    2.  **数据获取工具**:
        -   [ ] 创建 `src/lib/strapi.ts` (或 `src/utils/strapi.ts`)，封装 `fetch` 请求，并为 Strapi 数据定义清晰的 TypeScript 类型。
    3.  **首页动态化**:
        -   [ ] 改造首页的“精选作品”板块，调用 `strapi.ts` 中的函数从 Strapi 获取数据并渲染。
        -   [ ] 改造首页的“服务项目”板块，同样改为从 Strapi 获取数据。

### **Day 8: 动态作品集页面**
- **核心目标**: 完成网站最核心的**作品集**展示功能。
- **任务清单 (执行 `develop_plan.md` 任务 3.3 部分)**:
    1.  **作品列表页**:
        -   [ ] 创建 `src/pages/portfolio/index.astro`。
        -   [ ] 在页面中获取所有 Strapi 中的“作品”数据。
        -   [ ] 将获取到的数据传递给 Day 5 创建的 `PortfolioGrid.astro` 组件进行渲染。
    2.  **作品详情页 (动态路由)**:
        -   [ ] 创建 `src/pages/portfolio/[slug].astro`。
        -   [ ] 实现 `getStaticPaths`，为每个作品生成一个唯一的页面。
        -   [ ] 在页面中根据 `slug` 获取单个作品的详细数据（包括图片集、视频等），并进行精美排版展示。

### **Day 9: “开始我的定制”交互式表单 (Vue.js)**
- **核心目标**: 实现核心转化功能——引导式表单。
- **任务清单 (执行 `develop_plan.md` 任务 4.1)**:
    1.  **Vue 组件开发**:
        -   [ ] 安装 `@astrojs/vue` 集成。
        -   [ ] 在 `src/components/` 下创建 `CustomForm.vue` 组件。
        -   [ ] 设计并实现一个多步骤的引导式表单，收集用户需求。
    2.  **Astro 集成**:
        -   [ ] 创建一个新的页面，例如 `src/pages/start-customization.astro`，并将首页的 CTA 按钮链接到此页面。
        -   [ ] 在该页面中引入 `CustomForm.vue`，并使用 `client:load` 指令使其成为一个交互式的 Astro Island。
    3.  **表单提交**:
        -   [ ] **定义接口**: 与后端（或自己）定义好接收表单数据的 API 端点和数据格式。
        -   [ ] **实现提交**: 实现表单数据的客户端验证和 `fetch` 提交逻辑。初期可提交到免费表单服务（如 Formspree）或本地 Strapi 自定义端点进行测试。

---

## 第三阶段：创意优化与视觉增强 (MVP后)

### **Day 10-11: 页面细节优化**
- **核心目标**: 根据上线后的初步反馈和内部审查，对关键页面进行内容和样式的微调。
- **任务清单**:
    -   [ ] **内容更新**: 逐个页面审查文案、标语和描述，确保其完全符合品牌语调。
    -   [ ] **样式微调**: 调整组件的边距、颜色、字体大小等，提升视觉一致性和美感。
    -   [ ] **用户体验**: 优化按钮的交互反馈、表单的提示信息等。

### **Day 12-13: 3D 效果探索与实现**
- **核心目标**: 为网站引入引人注目的 3D 视觉元素，增强品牌艺术感和独特性。
- **任务清单**:
    -   [ ] **技术预研**: 调研并选择 TresJS (Vue + Three.js) 作为技术方案，评估其与 Astro 的集成方式和性能影响。
    -   [ ] **创意构思**: 确定最终的创意方案，例如“记忆放映机”或“灵感粒子汇聚”。
    -   [ ] **原型开发**: 在一个单独的测试页面中，开发一个 3D 展示效果的原型。
    -   [ ] **组件集成**: 将成熟的 3D 效果封装成一个独立的 Vue 组件，并集成到目标页面（如首页 Hero区域）。

---

## 第四阶段：最终测试与部署

### **Day 14: 最终优化与测试**
- **核心目标**: 全面提升网站质量，为上线做最后准备。
- **任务清单**:
    1.  **SEO 优化**: 重新检查所有页面的 title 和 meta description。
    2.  **无障碍访问 (a11y) 审查**: 根据 `GEMINI.md` #10，系统性检查全站 a11y 问题。
    3.  **功能与兼容性测试**: 在主流浏览器上完整测试所有页面和功能。
    4.  **性能优化**: 确保 3D 效果不会显著影响页面加载速度和性能，按需进行懒加载或优化。
    5.  **Bug修复**: 集中修复本轮测试发现的 Bug。

### **Day 15-16: 部署与复盘**
- **核心目标**: 顺利上线并进行项目总结。
- **任务清单**:
    -   **Day 15 (预发布与部署)**:
        -   [ ] 将 `develop` 分支合并到 `main`。
        -   [ ] 在 Vercel/Netlify 上部署预发布环境并进行最终测试。
        -   [ ] 执行生产环境部署，并在线上环境验证核心功能。
    -   **Day 16 (复盘与归档)**:
        -   [ ] 召开项目复盘会议，总结经验教训。
        -   [ ] 整理并归档所有最终版本的开发文档。