
# Astro 国际化 (i18n) 实践指南

本文档旨在为 **忆帧定制 (MemoFrame Studio)** 项目提供一个清晰、可执行的国际化（i18n）方案。基于 Astro 的特性，我们将利用其内置的路由和中间件能力，结合社区推荐的库来高效地实现多语言支持。

## 第一步：使用 AstroWind 模板初始化项目

AstroWind 是一个功能强大的入门模板，它内置了对国际化的支持，是我们的绝佳起点。

**执行以下命令来创建项目：**

```bash
npm create astro@latest -- --template onwidget/astrowind
```

在安装过程中，Astro CLI 会询问您一些问题（如项目名称、是否使用 TypeScript 等）。请根据您的需求进行选择。我们推荐使用严格模式的 TypeScript 来保证代码质量。

---

## 第二步：理解 AstroWind 中的国际化配置

AstroWind 已经为我们做好了基础配置。核心文件位于 `src/i18n/` 目录下。

1.  **语言配置文件**: `src/i18n/languages.ts`
    *   这个文件定义了您网站支持的所有语言。
    *   您可以修改它来添加或删除语言。例如，要同时支持简体中文和英文：
        ```typescript
        export const languages = {
          en: 'English',
          'zh-cn': '简体中文',
        };
        ```

2.  **UI 翻译文件**: `src/i18n/ui.ts`
    *   这个文件包含了网站中所有非内容部分的文本（例如：按钮、导航链接、页脚文本等）。
    *   您需要为每种支持的语言提供一个翻译对象。
        ```typescript
        export const ui = {
          en: {
            'nav.home': 'Home',
            'nav.services': 'Services',
            'cta.start': 'Start My Customization',
          },
          'zh-cn': {
            'nav.home': '首页',
            'nav.services': '服务项目',
            'cta.start': '开始我的定制',
          },
        } as const;
        ```

3.  **路由与翻译工具**: `src/i18n/utils.ts`
    *   这个文件提供了一些帮助函数，例如 `useTranslations`，它能根据当前页面的语言环境自动返回对应的翻译文本。

---

## 第三步：在组件和页面中使用翻译

配置好语言文件后，我们就可以在 Astro 组件（`.astro`）或框架组件（如 Vue, React）中使用了。

### 在 `.astro` 文件中

```astro
---
import { useTranslations }.from '~/i18n/utils';

// 假设当前是中文页面 (e.g., /zh-cn/)
const t = useTranslations('zh-cn'); 
---

<nav>
  <a href="/zh-cn/">{t('nav.home')}</a>
  <a href="/zh-cn/services">{t('nav.services')}</a>
</nav>

<button>{t('cta.start')}</button>
```

Astro 的中间件会自动检测 URL（例如 `site.com/en/about` 或 `site.com/zh-cn/about`）并提供正确的语言代码。

---

## 第四步：多语言内容（博客、页面）的管理

对于 Markdown 或 MDX 文件（例如博客文章或服务介绍页面），Astro 的内容集合（Content Collections）提供了完美的解决方案。

1.  **创建多语言内容目录**:
    在 `src/content/` 目录下，为不同语言创建不同的子目录。
    ```
    src/content/
    ├── blog/
    │   ├── en/
    │   │   ├── first-post.md
    │   │   └── second-post.md
    │   └── zh-cn/
    │       ├── first-post.md
    │       └── second-post.md
    └── pages/
        ├── en/
        │   └── about.md
        └── zh-cn/
            └── about.md
    ```

2.  **在动态路由中获取内容**:
    当您创建一个动态路由（例如 `src/pages/blog/[...slug].astro`）时，您可以根据 URL 中的语言参数来获取对应目录下的文章。

    ```astro
    ---
    import { getCollection } from 'astro:content';

    export async function getStaticPaths() {
      // ... 这里会生成 /en/blog/... 和 /zh-cn/blog/... 的路径
    }

    const { lang, slug } = Astro.params;
    const entry = await getEntryBySlug(`${lang}/blog`, slug);
    const { Content } = await entry.render();
    ---
    <Content />
    ```

---

## 第五步：实现语言切换器

为了让用户可以方便地在不同语言之间切换，您需要创建一个语言切换组件。

这个组件的逻辑是：
1.  获取当前页面的 URL。
2.  获取所有支持的语言。
3.  为每种语言生成一个链接，该链接指向当前页面对应的其他语言版本。

AstroWind 通常会自带一个基础的语言切换器，您可以根据品牌风格进行修改和美化。

---

## 总结

通过遵循以上步骤，您可以为您的 Astro 项目构建一个健壮、SEO友好且易于维护的国际化系统。

**核心要点**:
- **从 AstroWind 开始**: 它提供了坚实的基础。
- **中心化翻译**: 将所有 UI 文本放在 `src/i18n/ui.ts` 中统一管理。
- **内容分离**: 使用内容集合按语言组织您的 Markdown/MDX 内容。
- **利用 Astro 工具**: 善用 `useTranslations` 和 `getCollection` 等内置工具。

祝您开发顺利！
