# Astro + Cloudflare Pages 部署指南

本文档旨在为 “忆帧定制” 项目提供一份清晰、可重复的 Cloudflare Pages 部署流程备忘。

## 准备工作：修改 Astro 配置

为了使 Astro 项目能以最佳方式在 Cloudflare 的边缘网络上运行，我们推荐使用官方的 `@astrojs/cloudflare` 适配器。

### 步骤 1: 安装适配器

在项目根目录下，运行以下命令来安装 Cloudflare 适配器：

```bash
npm install @astrojs/cloudflare
```

### 步骤 2: 更新 `astro.config.ts`

打开 `astro.config.ts` 文件，进行如下修改：

1.  在文件顶部，引入 `cloudflare` 适配器。
2.  在 `defineConfig` 中，添加 `site` 属性，并修改 `output` 和 `adapter`。

```typescript
// astro.config.ts
import { defineConfig } from 'astro/config';
import cloudflare from '@astrojs/cloudflare';

// ... 其他 import ...

export default defineConfig({
  // 1. (重要) 替换成你 Cloudflare Pages 的最终域名
  site: 'https://your-project-name.pages.dev', 

  // 2. 将 output 修改为 'server'
  output: 'server',

  integrations: [
    // ... 其他集成 ...
    vue()
  ],

  // 3. 在 integrations 数组后面，添加 adapter
  adapter: cloudflare(),

  // ... 其他配置 ...
});
```

**注意**: 请务셔将 `site` 属性中的 URL 替换为您在 Cloudflare Pages 创建项目后得到的实际域名。

## Cloudflare 部署流程

当代码修改完成并推送到 GitHub/GitLab 仓库后，请遵循以下步骤在 Cloudflare 上进行部署：

1.  **登录 Cloudflare** 并导航至 **Workers & Pages**。
2.  点击 **Create application** > **Pages** > **Connect to Git**。
3.  **选择项目仓库** 并点击 `Begin setup`。
4.  **配置构建设置**:
    *   **Framework preset**: 从下拉列表中选择 **Astro**。
    *   **Build command**: Cloudflare 会自动设置为 `npm run build` 或 `astro build`。
    *   **Build output directory**: Cloudflare 会自动配置，您无需关心此项。
5.  **添加环境变量**:
    *   在 `Environment variables (advanced)` 部分，点击 `Add variable`。
    *   添加项目所需的所有环境变量，例如：
        *   `STRAPI_URL`: `https://your-strapi-backend.com`
        *   `STRAPI_API_TOKEN`: `your_strapi_api_token`
        *   `NODE_VERSION`: `18.17.1` 或更高版本。
6.  **保存并部署**: 点击 **Save and Deploy**。

Cloudflare 将自动拉取最新代码、安装依赖、构建项目并将其部署到全球网络。每次您向指定分支（如 `main` 或 `develop`）推送新的提交时，Cloudflare 都会自动重复此过程。
