# 本地开发环境临时共享指南 (内网穿透)

本文档旨在为 “忆帧定制” 项目提供一份清晰的、通过内网穿透来临时共享本地开发服务器的流程备忘。这对于向他人展示开发进度、或在移动设备上进行真机测试非常有用。

我们将使用 Cloudflare 的 `cloudflared` 工具。

## 准备工作：安装 `cloudflared`

如果您是第一次使用，需要先安装 `cloudflared` 命令行工具。

对于 macOS (根据您的系统环境)，最简单的方式是使用 Homebrew：

```bash
brew install cloudflared
```

如果您没有安装 Homebrew，可以参考 [Cloudflare 官方文档](https://developers.cloudflare.com/cloudflare-one/connections/connect-networks/install-and-setup/installation/) 进行安装。

### **重要**: 配置 Astro/Vite

为了让 Astro 的开发服务器允许来自 Cloudflare 隧道的请求，你需要修改 `astro.config.ts` 文件。

```typescript
// astro.config.ts
import { defineConfig } from 'astro/config';

export default defineConfig({
  // ... 其他配置 ...
  vite: {
    server: {
      host: true, // 允许通过网络 IP 地址访问
      // 为 Cloudflare Tunnel 添加白名单
      allowedHosts: ['.trycloudflare.com'],
    },
    // ... 其他 vite 配置 ...
  }
});
```
**如果进行了此项修改，请务必重启 Astro 开发服务器 (`npm run dev`)。**

## 使用流程

整个过程需要**两个**终端窗口。

### 步骤 1: 启动 Astro 开发服务器

在您的**第一个**终端窗口中，进入项目根目录，启动 Astro 的开发服务器：

```bash
npm run dev
```

启动后，您会看到类似下面的输出，代表您的网站正在本地 `http://localhost:4321/` 上运行：

```
  🚀  astro  v4.5.9  ready in 1.25s

  ┃ Local    http://localhost:4321/
  ┃ Network  use --host to expose
```

**请保持这个终端窗口的运行状态。**

### 步骤 2: 启动 Cloudflare Tunnel

现在，打开您的**第二个**终端窗口，运行以下命令：

这个命令的作用是告诉 `cloudflared`，将所有通过隧道过来的访问请求都转发到您本地的 `4321` 端口。

```bash
cloudflared tunnel --url http://localhost:4321
```

### 步骤 3: 获取并分享临时链接

命令运行成功后，`cloudflared` 会在终端中打印出一些日志信息，其中包含一个 `trycloudflare.com` 后缀的网址。这个网址就是您可以分享给别人的公开链接。

```
2024-03-20T14:30:00Z INF |  Your quick Tunnel has been created! Visit it at the following URL:
2024-03-20T14:30:00Z INF |  https://your-random-name-here.trycloudflare.com
```

**现在，任何人都可以通过上面这个 `https://...` 链接来访问您本地电脑上运行的网站了。**

当您想结束共享时，只需在第二个终端窗口中按下 `Ctrl + C` 即可关闭隧道。
