# 交互式3D动画开发计划

> **项目**: 忆帧定制 (MemoFrame Studio) 官网 - Hero Section 动画
> **目标**: 创建一个交互式的3D场景，其中一辆卡通汽车驶过屏幕，并伴随粉丝量等数据的动态弹出效果，以增强网站的品牌艺术感和吸引力。
> **周期**: 4天 (每天约6小时)
> **核心技术**: Three.js, Vue.js, GSAP (一个强大的动画库)

---

## 准备工作 (Day 0)

在开始之前，我们需要一个工具来帮助我们创建更流畅、更可控的动画时间线。我强烈推荐 **GSAP**。

**任务**:
1.  **安装 GSAP**: 在你的项目根目录下运行以下命令。
    ```bash
    npm install gsap
    ```
2.  **熟悉GSAP**: 花30分钟快速浏览 [GSAP官方文档的 "Getting Started" 部分](https://gsap.com/docs/v3/get-started/)，理解其核心概念 `Tween` 和 `Timeline`。

---

## Day 1: 动画基础与路径创建 (6小时)

**目标**: 让小车沿着预设的直线路径，平滑地从屏幕一端移动到另一端。

*   **任务 1: 创建动画循环 (2小时)**
    *   在你现有的 `CameraModel.vue` 组件中，引入 `gsap`。
    *   在 `onMounted` hook 中，使用 `gsap.to()` 方法来创建一个简单的动画。
    *   目标是你的汽车模型 (`gltf.scene`) 的 `position` 属性。
    *   设置一个持续时间（例如 `duration: 5`），让小车在5秒内，`position.x` 从一个初始值（例如 `-5`）变化到目标值（例如 `5`）。
    *   **产出**: 小车可以在页面加载后，自动从左向右移动。

*   **任务 2: 调整相机与光照 (2小时)**
    *   固定你的相机位置，确保它能完整地捕捉到小车从起点到终点的整个运动过程。你可能需要稍微拉远或调整相机的角度。
    *   检查当前光照。当小车移动时，确保它的所有部分都清晰可见，没有出现过暗或过曝的情况。可以考虑添加一个跟随车辆的 `PointLight` (点光源) 来补充照明。
    *   **产出**: 动画的“舞台”已经搭建好，镜头和灯光就位。

*   **任务 3: 完善动画路径与缓动 (2小时)**
    *   使用 `gsap` 的 `ease` 属性来给动画添加缓动效果。例如 `ease: "power2.inOut"`，这会让小车的启动和停止看起来更自然，而不是僵硬的匀速运动。
    *   尝试不同的 `ease` 类型，找到最符合你品牌“温暖、艺术感”的动态感觉。
    *   **产出**: 小车的移动变得生动、有节奏感。

---

## Day 2: 2D界面元素与基础同步 (6小时)

**目标**: 创建要在动画期间弹出的2D信息（如粉丝数），并实现与3D动画的初步同步。

*   **任务 1: 创建2D UI元素 (2小时)**
    *   在 `CameraModel.vue` 的 `<template>` 部分，在3D画布的外部或上层，创建你想要展示的HTML元素。
    *   例如:
        ```html
        <div class="stats-container">
          <div id="stat-xiaohongshu" class="stat">小红书粉丝: 10k+</div>
          <div id="stat-tiktok" class="stat">TikTok 粉丝: 8k+</div>
        </div>
        ```
    *   使用CSS (Tailwind) 对它们进行样式设计，并使用 `position: absolute` 将它们定位在你希望它们出现的位置。
    *   初始状态下，将它们设置为不可见 (`opacity: 0; transform: scale(0.5);`)。
    *   **产出**: 漂亮的、但暂时隐藏的2D信息元素。

*   **任务 2: 使用 GSAP Timeline 管理动画 (4小时)**
    *   这是最关键的一步。创建一个 `gsap.timeline()` 实例。
    *   将昨天的“小车移动动画”添加到这个时间线中。
    *   使用时间线的“位置参数”，在小车动画开始后的特定时间点，添加2D元素的“弹出动画”。
    *   示例代码:
        ```javascript
        const tl = gsap.timeline();
        // 小车动画在0秒开始
        tl.to(gltf.scene.position, { x: 5, duration: 5, ease: "power2.inOut" });
        // 在1.5秒时，小红书的统计数据弹出
        tl.to("#stat-xiaohongshu", { opacity: 1, scale: 1, duration: 0.5, ease: "elastic.out(1, 0.5)" }, 1.5);
        // 在2.5秒时，TikTok的统计数据弹出
        tl.to("#stat-tiktok", { opacity: 1, scale: 1, duration: 0.5, ease: "elastic.out(1, 0.5)" }, 2.5);
        ```
    *   **产出**: 一个完整的、经过编排的动画序列：小车行驶，途中依次弹出各项数据。

---

## Day 3: 交互性与细节打磨 (6小时)

**目标**: 让动画更智能，并打磨所有视觉细节，使其达到可交付的质量。

*   **任务 1: 滚动触发动画 (3小时)**
    *   默认情况下，动画不播放。只有当用户滚动页面，该组件进入视口 (viewport) 时才开始播放。
    *   使用 `Intersection Observer` API 来实现这个功能。
    *   在 `onMounted` 中，创建一个 `IntersectionObserver` 来观察你的组件根元素。
    *   当元素进入视口时，调用 `timeline.play()`。当它离开视口时，可以选择调用 `timeline.pause()` 或 `timeline.restart()`。
    *   **产出**: 动画变得更智能，有效提升了页面性能和用户体验。

*   **任务 2: 增加微妙的3D细节 (2小时)**
    *   给小车增加一点额外的、微妙的动画，提升真实感。
    *   例如，在时间线开始时，让小车的车身轻微向后倾斜一下再前进，模拟加速时的物理效果。
    *   可以使用 `gsap` 同时改变模型的 `rotation` 和 `position` 属性。
    *   **产出**: 动画细节更丰富，质感更好。

*   **任务 3: 响应式布局调整 (1小时)**
    *   检查在不同屏幕尺寸下（特别是移动端），你的动画和2D元素是否依然表现良好。
    *   你可能需要使用媒体查询或在JS中判断窗口大小，来调整3D场景的大小或2D元素的字体/位置。
    *   **产出**: 动画在所有设备上都能完美展示。

---

## Day 4: 性能优化与代码整理 (6小时)

**目标**: 确保动画流畅不卡顿，代码清晰易维护。

*   **任务 1: 性能检查与优化 (3小时)**
    *   打开浏览器的开发者工具，使用 Performance 面板来检查动画播放期间的帧率 (FPS)。确保它能稳定在 60FPS 左右。
    *   **关键**: 在Vue组件的 `onUnmounted` hook 中，正确销毁Three.js的场景、渲染器、材质和几何体，防止内存泄漏。这是一个非常重要的步骤。
        ```javascript
        // 伪代码
        onUnmounted(() => {
          renderer.dispose();
          scene.traverse(object => {
            if (object.isMesh) {
              object.geometry.dispose();
              object.material.dispose();
            }
          });
        });
        ```
    *   **产出**: 一个高性能、无内存泄漏的稳定组件。

*   **任务 2: 代码重构与注释 (2小时)**
    *   整理你的代码，将Three.js的初始化逻辑、GSAP的timeline逻辑等封装到独立的函数中，使 `onMounted` 看起来更清晰。
    *   为关键代码块（特别是GSAP时间线和Three.js对象销毁部分）添加清晰的注释。
    *   **产出**: 专业、可维护的代码。

*   **任务 3: 最终审查与合并 (1小时)**
    *   全面测试所有功能：动画播放、滚动触发、浏览器窗口缩放等。
    *   确认一切完美后，将代码合并到你的主开发分支。
    *   **恭喜你，大功告成！**

