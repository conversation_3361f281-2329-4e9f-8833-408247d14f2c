{"name": "three-custom-shader-material", "private": false, "version": "5.4.0", "description": "Extend Three.js standard materials with your own shaders!", "main": "dist/three-custom-shader-material.cjs.js", "module": "dist/three-custom-shader-material.esm.js", "types": "dist/three-custom-shader-material.cjs.d.ts", "files": ["dist/**", "vanilla/**", "README.md", "LICENSE.md", "package.json"], "preconstruct": {"entrypoints": ["index.tsx", "vanilla.ts"]}, "repository": {"type": "git", "url": "git+https://github.com/FarazzShaikh/THREE-CustomShaderMaterial"}, "bugs": {"url": "https://github.com/FarazzShaikh/THREE-CustomShaderMaterial/issues"}, "author": "<PERSON><PERSON> <faraz<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "homepage": "https://github.com/FarazzShaikh/THREE-CustomShaderMaterial#readme", "keywords": ["react", "webgl", "three", "custom", "shaders", "shader", "materials", "material", "procedural"], "scripts": {"dev": "preconstruct watch", "build": "preconstruct build"}, "devDependencies": {"@babel/core": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.17.12", "@react-three/fiber": "^8.13.4", "@types/node": "^18.0.6", "@types/object-hash": "^2.2.1", "@types/react": "^18.0.17", "@types/three": "^0.153.0", "json": "^11.0.0", "react": "^18.0.0", "three": "^0.154.0", "typescript": "^4.7.4"}, "peerDependencies": {"@react-three/fiber": ">=8.0", "react": ">=18.0", "three": ">=0.154"}, "peerDependenciesMeta": {"@react-three/fiber": {"optional": true}, "react": {"optional": true}}, "dependencies": {"glsl-token-functions": "^1.0.1", "glsl-token-string": "^1.0.1", "glsl-tokenizer": "^2.1.5", "object-hash": "^3.0.0"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-react", ["@babel/preset-typescript", {"isTSX": true, "allExtensions": true}]]}}