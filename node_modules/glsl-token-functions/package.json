{"name": "glsl-token-functions", "version": "1.0.1", "description": "Extract function definitions from an array of GLSL tokens.", "main": "index.js", "license": "MIT", "scripts": {"test": "node test | tap-spec", "posttest": "standard"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/hughsk"}, "dependencies": {}, "devDependencies": {"glsl-token-string": "^1.0.1", "glsl-tokenizer": "^2.0.2", "standard": "^8.4.0", "tap-spec": "^4.1.0", "tape": "^4.2.2"}, "repository": {"type": "git", "url": "git://github.com/stackgl/glsl-token-functions.git"}, "keywords": ["ecosystem:stackgl", "glsl", "tokens", "analyze", "function", "language", "shader", "arguments"], "homepage": "https://github.com/stackgl/glsl-token-functions", "bugs": {"url": "https://github.com/stackgl/glsl-token-functions/issues"}}