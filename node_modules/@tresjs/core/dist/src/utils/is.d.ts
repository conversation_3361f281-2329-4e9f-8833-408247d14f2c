import { TresObject, TresPrimitive } from 'src/types';
import { BufferGeometry, Camera, Color, ColorRepresentation, Fog, Light, Material, Object3D, Scene, Layers } from 'three';
export declare function und(u: unknown): u is undefined;
export declare function arr(u: unknown): u is Array<unknown>;
export declare function num(u: unknown): u is number;
export declare function str(u: unknown): u is string;
export declare function bool(u: unknown): u is boolean;
export declare function fun(u: unknown): u is (...args: any[]) => any;
export declare function obj(u: unknown): u is Record<string | number | symbol, unknown>;
export declare function object3D(u: unknown): u is Object3D;
export declare function color(u: unknown): u is Color;
export declare function colorRepresentation(u: unknown): u is ColorRepresentation;
interface VectorLike {
    set: (...args: any[]) => void;
    constructor?: (...args: any[]) => any;
}
export declare function vectorLike(u: unknown): u is VectorLike;
interface Copyable {
    copy: (...args: any[]) => void;
    constructor?: (...args: any[]) => any;
}
export declare function copyable(u: unknown): u is Copyable;
interface ClassInstance {
    constructor?: (...args: any[]) => any;
}
export declare function classInstance(object: unknown): object is ClassInstance;
export declare function layers(u: unknown): u is Layers;
export declare function camera(u: unknown): u is Camera;
export declare function bufferGeometry(u: unknown): u is BufferGeometry;
export declare function material(u: unknown): u is Material;
export declare function light(u: unknown): u is Light;
export declare function fog(u: unknown): u is Fog;
export declare function scene(u: unknown): u is Scene;
export declare function tresObject(u: unknown): u is TresObject;
export declare function tresPrimitive(u: unknown): u is TresPrimitive;
export {};
