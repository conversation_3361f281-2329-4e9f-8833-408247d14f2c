/**
 * name: @tresjs/core
 * version: v4.3.6
 * (c) 2025
 * description: Declarative ThreeJS using Vue Components
 * author: <PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)
 */
var At = Object.defineProperty;
var xt = (e, t, r) => t in e ? At(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var de = (e, t, r) => xt(e, typeof t != "symbol" ? t + "" : t, r);
import { defineComponent as ue, withAsyncContext as ot, reactive as Se, renderSlot as st, unref as W, ref as $, computed as q, watchEffect as Ae, onUnmounted as K, shallowRef as H, watch as ie, readonly as _e, provide as ae, inject as kt, isRef as Lt, useSlots as Rt, getCurrentInstance as Ue, onMounted as Ot, createElementBlock as Dt, openBlock as jt, normalizeStyle as Bt, normalizeClass as It, createRenderer as $t, h as Fe, Fragment as Ht } from "vue";
import * as it from "three";
import { Layers as Ut, Scene as at, MathUtils as lt, Vector3 as xe, MeshBasicMaterial as ct, DoubleSide as Ft, TextureLoader as Wt, PerspectiveCamera as we, Camera as Nt, Clock as ut, REVISION as zt, Color as Z, NoToneMapping as Gt, PCFSoftShadowMap as Vt, ACESFilmicToneMapping as ft, SRGBColorSpace as Yt, WebGLRenderer as me, Vector2 as ge, Raycaster as qt, Object3D as Kt, BufferAttribute as Jt, ArrowHelper as Qt, Line as Xt, BufferGeometry as We, Float32BufferAttribute as Ne, LineBasicMaterial as Zt, Mesh as er, BackSide as tr, HemisphereLightHelper as rr, SpotLightHelper as nr, PointLightHelper as or, DirectionalLightHelper as sr } from "three";
import { tryOnScopeDispose as ir, toValue as A, unrefElement as ar, useDevicePixelRatio as lr, useWindowSize as cr, useElementSize as ur, refDebounced as ze, usePointer as fr, useElementBounding as pr, createEventHook as D, useFps as dr, useMemory as mr, useRafFn as pt } from "@vueuse/core";
const gr = "@tresjs/core", hr = "module", vr = "4.3.6", yr = "pnpm@10.6.3", _r = "Declarative ThreeJS using Vue Components", wr = "Alvaro Saburido <<EMAIL>> (https://github.com/alvarosabu/)", br = "MIT", Mr = { type: "git", url: "git+https://github.com/Tresjs/tres.git" }, Pr = ["vue", "3d", "threejs", "three", "threejs-vue"], Cr = !1, Er = { ".": { types: "./dist/index.d.ts", import: "./dist/tres.js", require: "./dist/tres.umd.cjs" }, "./components": { types: "./dist/src/components/index.d.ts" }, "./composables": { types: "./dist/src/composables/index.d.ts" }, "./types": { types: "./dist/src/types/index.d.ts" }, "./utils": { types: "./dist/src/utils/index.d.ts" }, "./*": "./*" }, Tr = "./dist/tres.js", Sr = "./dist/tres.js", Ar = "./dist/index.d.ts", xr = ["*.d.ts", "dist"], kr = { access: "public" }, Lr = { dev: "pnpm --filter='./playground/vue' dev", "dev:nuxt": "pnpm --filter='./playground/nuxt' dev", build: "vite build", test: "vitest", "test:ci": "vitest run", "test:ui": "vitest --ui --coverage.enabled=true", release: "release-it", coverage: "vitest run --coverage", lint: "eslint .", "lint:fix": "eslint . --fix", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "vitepress serve docs", "docs:preview": "vitepress preview docs", "docs:contributors": "esno scripts/update-contributors.ts" }, Rr = { three: ">=0.133", vue: ">=3.4" }, Or = { "@alvarosabu/utils": "^3.2.0", "@vue/devtools-api": "^6.6.3", "@vueuse/core": "^12.5.0" }, Dr = { "@release-it/conventional-changelog": "^10.0.0", "@stackblitz/sdk": "^1.11.0", "@tresjs/cientos": "4.1.0", "@tresjs/eslint-config": "^1.4.0", "@types/three": "^0.173.0", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.5", "@vue/test-utils": "^2.4.6", eslint: "^9.19.0", "eslint-plugin-vue": "^9.32.0", esno: "^4.8.0", gsap: "^3.12.7", jsdom: "^26.0.0", kolorist: "^1.8.0", ohmyfetch: "^0.4.21", pathe: "^2.0.2", "release-it": "^18.1.2", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.14.0", sponsorkit: "^16.3.0", three: "^0.173.0", unocss: "^65.4.3", unplugin: "^2.1.2", "unplugin-vue-components": "^28.0.0", vite: "^6.1.0", "vite-plugin-banner": "^0.8.0", "vite-plugin-dts": "4.5.0", "vite-plugin-inspect": "^10.1.0", "vite-plugin-require-transform": "^1.0.21", "vite-svg-loader": "^5.1.0", vitepress: "1.6.3", vitest: "3.0.5", vue: "3.5.13", "vue-demi": "^0.14.10" }, jr = {
  name: gr,
  type: hr,
  version: vr,
  packageManager: yr,
  description: _r,
  author: wr,
  license: br,
  repository: Mr,
  keywords: Pr,
  sideEffects: Cr,
  exports: Er,
  main: Tr,
  module: Sr,
  types: Ar,
  files: xr,
  publishConfig: kr,
  scripts: Lr,
  peerDependencies: Rr,
  dependencies: Or,
  devDependencies: Dr
};
function Br(e) {
  const t = { nodes: {}, materials: {} };
  return e && e.traverse((r) => {
    r.name && (t.nodes[r.name] = r), r.material && !t.materials[r.material.name] && (t.materials[r.material.name] = r.material);
  }), t;
}
async function Ir(e, t, r, n, o) {
  const { logError: l } = Q(), s = new e();
  return o && o(s), r && r(s), await new Promise((a, c) => {
    s.load(
      t,
      (i) => {
        const f = i;
        f.scene && Object.assign(f, Br(f.scene)), a(f);
      },
      n,
      (i) => {
        l("[useLoader] - Failed to load resource", i), c(i);
      }
    );
  });
}
const eo = /* @__PURE__ */ ue({
  __name: "component",
  props: {
    loader: {},
    url: {}
  },
  async setup(e) {
    let t, r;
    const n = e, o = ([t, r] = ot(() => Se(Ir(n.loader, n.url))), t = await t, r(), t);
    return (l, s) => st(l.$slots, "default", { data: W(o) });
  }
});
class $r extends it.Mesh {
  constructor(...r) {
    super(...r);
    de(this, "type", "HightlightMesh");
    de(this, "createTime");
    this.createTime = Date.now();
  }
  onBeforeRender() {
    const n = (Date.now() - this.createTime) / 1e3, s = 1 + 0.07 * Math.sin(2.5 * n);
    this.scale.set(s, s, s);
  }
}
function be(e) {
  return typeof e > "u";
}
function ke(e) {
  return Array.isArray(e);
}
function Hr(e) {
  return typeof e == "number";
}
function dt(e) {
  return typeof e == "string";
}
function Y(e) {
  return typeof e == "function";
}
function j(e) {
  return e === Object(e) && !ke(e) && !Y(e);
}
function N(e) {
  return j(e) && !!e.isObject3D;
}
function mt(e) {
  return j(e) && !!e.isColor;
}
function Ur(e) {
  return e != null && (typeof e == "string" || typeof e == "number" || mt(e));
}
function Me(e) {
  return e !== null && typeof e == "object" && "set" in e && typeof e.set == "function";
}
function Fr(e) {
  return Me(e) && "copy" in e && typeof e.copy == "function";
}
function Wr(e) {
  return !!(e != null && e.constructor);
}
function Ge(e) {
  return e instanceof Ut;
}
function Ve(e) {
  return j(e) && !!e.isCamera;
}
function Nr(e) {
  return j(e) && !!e.isBufferGeometry;
}
function zr(e) {
  return j(e) && !!e.isMaterial;
}
function Gr(e) {
  return j(e) && !!e.isLight;
}
function Vr(e) {
  return j(e) && !!e.isFog;
}
function Yr(e) {
  return j(e) && !!e.isScene;
}
function ne(e) {
  return N(e) || Nr(e) || zr(e) || Vr(e);
}
function qr(e) {
  return j(e) && !!e.isPrimitive;
}
const gt = (e, t) => {
  for (const r of Object.keys(t))
    t[r] instanceof Object && Object.assign(t[r], gt(e[r], t[r]));
  return Object.assign(e || {}, t), e;
}, Kr = "html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot", Jr = /* @__PURE__ */ Zr(Kr);
function Ye(e) {
  return e && e.nodeType === 1;
}
function he(e) {
  return e.replace(/-([a-z])/g, (t, r) => r.toUpperCase());
}
const Qr = /\B([A-Z])/g;
function Xr(e) {
  return e.replace(Qr, "-$1").toLowerCase();
}
function Zr(e, t) {
  const r = /* @__PURE__ */ Object.create(null), n = e.split(",");
  for (let o = 0; o < n.length; o++)
    r[n[o]] = !0;
  return t ? (o) => !!r[o.toLowerCase()] : (o) => !!r[o];
}
const qe = (e, t) => {
  if (!t)
    return;
  const r = Array.isArray(t) ? t : t.match(/([^[.\]])+/g);
  return r == null ? void 0 : r.reduce((n, o) => n && n[o], e);
}, en = (e, t, r) => {
  const n = Array.isArray(t) ? t : t.match(/([^[.\]])+/g);
  n && n.reduce((o, l, s) => (o[l] === void 0 && (o[l] = {}), s === n.length - 1 && (o[l] = r), o[l]), e);
};
function ht(e, t) {
  if (Ye(e) && Ye(t)) {
    const o = e.attributes, l = t.attributes;
    return o.length !== l.length ? !1 : Array.from(o).every(({ name: s, value: a }) => t.getAttribute(s) === a);
  }
  if (e === t)
    return !0;
  if (e === null || typeof e != "object" || t === null || typeof t != "object")
    return !1;
  const r = Object.keys(e), n = Object.keys(t);
  if (r.length !== n.length)
    return !1;
  for (const o of r)
    if (!n.includes(o) || !ht(e[o], t[o]))
      return !1;
  return !0;
}
function tn(e, t) {
  if (!Array.isArray(e) || !Array.isArray(t) || e.length !== t.length)
    return !1;
  for (let r = 0; r < e.length; r++)
    if (!ht(e[r], t[r]))
      return !1;
  return !0;
}
const rn = Array.isArray;
function nn(e, t, r, n) {
  const o = (c) => {
    if (c.uuid === t)
      return c;
    for (const i of c.children) {
      const f = o(i);
      if (f)
        return f;
    }
  }, l = o(e);
  if (!l) {
    console.warn("Object with UUID not found in the scene.");
    return;
  }
  let s = l;
  for (let c = 0; c < r.length - 1; c++)
    if (s[r[c]] !== void 0)
      s = s[r[c]];
    else {
      console.warn(`Property path is not valid: ${r.join(".")}`);
      return;
    }
  const a = r[r.length - 1];
  s[a] !== void 0 ? s[a] = n : console.warn(`Property path is not valid: ${r.join(".")}`);
}
function on(e) {
  const t = new ct({
    color: 11003607,
    // Highlight color, e.g., yellow
    transparent: !0,
    opacity: 0.2,
    depthTest: !1,
    // So the highlight is always visible
    side: Ft
    // To e
  });
  return new $r(e.geometry.clone(), t);
}
function sn(e) {
  var r;
  let t = e.value;
  return e.value && ((r = e.value) != null && r.isMesh) && (t = e.value.position), Array.isArray(e.value) && (t = new xe(...t)), t;
}
function an(e) {
  return "map" in e;
}
function Ke(e) {
  an(e) && e.map && e.map.dispose(), e.dispose();
}
function vt(e) {
  var r, n;
  if (e.parent && ((r = e.removeFromParent) == null || r.call(e)), delete e.__tres, [...e.children].forEach((o) => vt(o)), !(e instanceof at)) {
    const o = e;
    e && ((n = e.dispose) == null || n.call(e)), o.geometry && o.geometry.dispose(), Array.isArray(o.material) ? o.material.forEach((l) => Ke(l)) : o.material && Ke(o.material);
  }
}
function ln(e, t) {
  let r = 0;
  for (let n = 0; n < e.length; n++)
    t(e[n], n) && (e[r] = e[n], r++);
  return e.length = r, e;
}
function Pe(e, t) {
  let r = e;
  if (t.includes("-")) {
    const n = t.split("-");
    let o = n.shift();
    for (; r && n.length; )
      o in r ? (r = r[o], o = n.shift()) : o = Je(o, n.shift());
    return { target: r, key: Je(o, ...n) };
  } else
    return { target: r, key: t };
}
function Je(...e) {
  return e.map((t, r) => r === 0 ? t : t.charAt(0).toUpperCase() + t.slice(1)).join("");
}
const Qe = /-\d+$/;
function cn(e, t, r) {
  if (dt(r)) {
    if (Qe.test(r)) {
      const l = r.replace(Qe, ""), { target: s, key: a } = Pe(e, l);
      if (!Array.isArray(s[a])) {
        const c = s[a], i = [];
        i.__tresDetach = () => {
          i.every((f) => be(f)) && (s[a] = c);
        }, s[a] = i;
      }
    }
    const { target: n, key: o } = Pe(e, r);
    t.__tres.previousAttach = n[o], n[o] = J(t);
  } else
    t.__tres.previousAttach = r(e, t);
}
function un(e, t, r) {
  var n, o, l;
  if (dt(r)) {
    const { target: s, key: a } = Pe(e, r), c = t.__tres.previousAttach;
    c === void 0 ? delete s[a] : s[a] = c, "__tresDetach" in s && s.__tresDetach();
  } else
    (o = (n = t.__tres) == null ? void 0 : n.previousAttach) == null || o.call(n, e, t);
  (l = t.__tres) == null || delete l.previousAttach;
}
function z(e, t, r) {
  const n = e;
  return n.__tres = {
    type: "unknown",
    eventCount: 0,
    root: r,
    handlers: {},
    memoizedProps: {},
    objects: [],
    parent: null,
    previousAttach: null,
    ...t
  }, n.__tres.attach || (n.isMaterial ? n.__tres.attach = "material" : n.isBufferGeometry ? n.__tres.attach = "geometry" : n.isFog && (n.__tres.attach = "fog")), n;
}
function yt(e) {
  var r;
  const t = (r = e == null ? void 0 : e.__tres) == null ? void 0 : r.root;
  t && t.render && t.render.canBeInvalidated.value && t.invalidate();
}
function fn(e, t, r) {
  var o;
  if (!Y(e.setPixelRatio))
    return;
  let n = 0;
  if (r && ke(r) && r.length >= 2) {
    const [l, s] = r;
    n = lt.clamp(t, l, s);
  } else Hr(r) ? n = r : n = t;
  n !== ((o = e.getPixelRatio) == null ? void 0 : o.call(e)) && e.setPixelRatio(n);
}
function pn(e, t, r, n, o) {
  const l = [...t.__tres.objects], s = J(t);
  if (e = J(e), s === e)
    return !0;
  const a = z(e, t.__tres ?? {}, o), c = t.parent ?? t.__tres.parent ?? null, i = { ...t.__tres.memoizedProps };
  delete i.object;
  for (const f of l)
    _t(f, o), wt(f, o);
  s.__tres.objects = [], n.remove(t);
  for (const [f, v] of Object.entries(i))
    n.patchProp(a, f, a[f], v);
  r(e), n.insert(t, c);
  for (const f of l)
    n.insert(f, t);
  return !0;
}
function J(e) {
  return qr(e) ? (e.object.__tres = e.__tres, e.object) : e;
}
function _t(e, t) {
  var n, o, l, s;
  const r = ((n = e.__tres) == null ? void 0 : n.parent) || t.scene.value;
  e.__tres && (e.__tres.parent = null), r && r.__tres && "objects" in r.__tres && ln(r.__tres.objects, (a) => a !== e), (o = e.__tres) != null && o.attach ? un(r, e, e.__tres.attach) : ((s = (l = e.parent) == null ? void 0 : l.remove) == null || s.call(l, J(e)), e.parent = null);
}
function wt(e, t) {
  var r;
  (r = e.traverse) == null || r.call(e, (n) => {
    var o;
    t.deregisterCamera(n), (o = t.eventManager) == null || o.deregisterPointerMissedObject(n);
  }), t.deregisterCamera(e), yt(e);
}
async function dn(e, t) {
  const r = new Wt(t), n = (o) => new Promise((l, s) => {
    r.load(
      o,
      (a) => l(a),
      () => null,
      () => {
        s(new Error("[useTextures] - Failed to load texture"));
      }
    );
  });
  if (rn(e)) {
    const o = await Promise.all(e.map((l) => n(l)));
    return e.length > 1 ? o : o[0];
  } else {
    const {
      map: o,
      displacementMap: l,
      normalMap: s,
      roughnessMap: a,
      metalnessMap: c,
      aoMap: i,
      alphaMap: f,
      matcap: v
    } = e;
    return {
      map: o ? await n(o) : null,
      displacementMap: l ? await n(l) : null,
      normalMap: s ? await n(s) : null,
      roughnessMap: a ? await n(a) : null,
      metalnessMap: c ? await n(c) : null,
      aoMap: i ? await n(i) : null,
      alphaMap: f ? await n(f) : null,
      matcap: v ? await n(v) : null
    };
  }
}
const to = /* @__PURE__ */ ue({
  __name: "component",
  props: {
    map: {},
    displacementMap: {},
    normalMap: {},
    roughnessMap: {},
    metalnessMap: {},
    aoMap: {},
    alphaMap: {},
    matcap: {}
  },
  async setup(e) {
    let t, r;
    const n = e, o = ([t, r] = ot(() => Se(dn(n))), t = await t, r(), t);
    return (l, s) => st(l.$slots, "default", { textures: W(o) });
  }
}), mn = ({ sizes: e }) => {
  const t = $([]), r = q(
    () => t.value[0]
  ), n = (s) => {
    const a = s instanceof Nt ? s : t.value.find((i) => i.uuid === s);
    if (!a)
      return;
    const c = t.value.filter(({ uuid: i }) => i !== a.uuid);
    t.value = [a, ...c];
  }, o = (s, a = !1) => {
    if (Ve(s)) {
      const c = s;
      if (t.value.some(({ uuid: i }) => i === c.uuid))
        return;
      a ? n(c) : t.value.push(c);
    }
  }, l = (s) => {
    if (Ve(s)) {
      const a = s;
      t.value = t.value.filter(({ uuid: c }) => c !== a.uuid);
    }
  };
  return Ae(() => {
    e.aspectRatio.value && t.value.forEach((s) => {
      !s.manual && (s instanceof we || gn(s)) && (s instanceof we ? s.aspect = e.aspectRatio.value : (s.left = e.width.value * -0.5, s.right = e.width.value * 0.5, s.top = e.height.value * 0.5, s.bottom = e.height.value * -0.5), s.updateProjectionMatrix());
    });
  }), K(() => {
    t.value = [];
  }), {
    camera: r,
    cameras: t,
    registerCamera: o,
    deregisterCamera: l,
    setCameraActive: n
  };
};
function gn(e) {
  return e.hasOwnProperty("isOrthographicCamera") && e.isOrthographicCamera;
}
const ro = !0, fe = "[TresJS ▲ ■ ●] ";
function hn(...e) {
  typeof e[0] == "string" ? e[0] = fe + e[0] : e.unshift(fe), console.error(...e);
}
function vn(...e) {
  typeof e[0] == "string" ? e[0] = fe + e[0] : e.unshift(fe), console.warn(...e);
}
function yn(e, t) {
}
function Q() {
  return {
    logError: hn,
    logWarning: vn,
    logMessage: yn
  };
}
const Ce = $({}), Ee = (e) => Object.assign(Ce.value, e);
function ve() {
  const e = /* @__PURE__ */ new Map(), t = /* @__PURE__ */ new Set();
  let r = 0, n = !1;
  const o = () => {
    const i = Array.from(e.entries()).sort((f, v) => {
      const u = f[1].priority - v[1].priority;
      return u === 0 ? f[1].addI - v[1].addI : u;
    });
    t.clear(), i.forEach((f) => t.add(f[0]));
  }, l = (i) => {
    e.delete(i), t.delete(i);
  };
  return { on: (i, f = 0) => {
    e.set(i, { priority: f, addI: r++ });
    const v = () => l(i);
    return ir(v), n = !0, {
      off: v
    };
  }, off: l, trigger: (...i) => {
    n && (o(), n = !1), t.forEach((f) => f(...i));
  }, dispose: () => {
    e.clear(), t.clear();
  }, get count() {
    return e.size;
  } };
}
function _n() {
  let e = !0, t = !0, r = !1;
  const n = new ut(!1), o = $(n.running), l = $(!1);
  let s;
  const a = lt.generateUUID();
  let c = null;
  const i = ve(), f = ve(), v = ve();
  S();
  let u = {};
  function _(M) {
    u = M;
  }
  function d(M, x, m = 0) {
    switch (x) {
      case "before":
        return i.on(M, m);
      case "render":
        return c || (c = M), f.dispose(), f.on(M);
      case "after":
        return v.on(M, m);
    }
  }
  function y() {
    t && (t = !1, S(), E());
  }
  function p() {
    t = !0, S(), cancelAnimationFrame(s);
  }
  function h() {
    r = !1, S();
  }
  function P() {
    r = !0, S();
  }
  function C() {
    l.value = !0;
  }
  function b() {
    l.value = !1;
  }
  function E() {
    if (!e) {
      s = requestAnimationFrame(E);
      return;
    }
    const M = n.getDelta(), x = n.getElapsedTime(), m = {
      camera: W(u.camera),
      scene: W(u.scene),
      renderer: W(u.renderer),
      raycaster: W(u.raycaster),
      controls: W(u.controls),
      invalidate: u.invalidate,
      advance: u.advance
    }, w = { delta: M, elapsed: x, clock: n, ...m };
    o.value && i.trigger(w), l.value || (f.count ? f.trigger(w) : c && c(w)), o.value && v.trigger(w), s = requestAnimationFrame(E);
  }
  function S() {
    const M = !t && !r;
    n.running !== M && (n.running ? n.stop() : n.start()), o.value = n.running;
  }
  return {
    loopId: a,
    register: (M, x, m) => d(M, x, m),
    start: y,
    stop: p,
    pause: P,
    resume: h,
    pauseRender: C,
    resumeRender: b,
    isRenderPaused: l,
    isActive: o,
    setContext: _,
    setReady: (M) => e = M
  };
}
function Le(e) {
  let t = 0;
  return e.traverse((r) => {
    if (r.isMesh && r.geometry && r.type !== "HightlightMesh") {
      const n = r.geometry, o = n.attributes.position.count * 3 * Float32Array.BYTES_PER_ELEMENT, l = n.index ? n.index.count * Uint32Array.BYTES_PER_ELEMENT : 0, s = n.attributes.normal ? n.attributes.normal.count * 3 * Float32Array.BYTES_PER_ELEMENT : 0, a = n.attributes.uv ? n.attributes.uv.count * 2 * Float32Array.BYTES_PER_ELEMENT : 0, c = o + l + s + a;
      t += c;
    }
  }), t;
}
function wn(e) {
  return (e / 1024).toFixed(2);
}
const bn = Number.parseInt(zt.replace("dev", ""));
function no(e) {
  return typeof e == "number" ? [e, e, e] : e instanceof xe ? [e.x, e.y, e.z] : e;
}
function Mn(e) {
  return e instanceof Z ? e : Array.isArray(e) ? new Z(...e) : new Z(e);
}
const oe = {
  realistic: {
    shadows: !0,
    physicallyCorrectLights: !0,
    outputColorSpace: Yt,
    toneMapping: ft,
    toneMappingExposure: 3,
    shadowMap: {
      enabled: !0,
      type: Vt
    }
  },
  flat: {
    toneMapping: Gt,
    toneMappingExposure: 1
  }
};
function Pn({
  canvas: e,
  options: t,
  contextParts: { sizes: r, render: n, invalidate: o, advance: l }
}) {
  const s = q(() => ({
    alpha: A(t.alpha) ?? !0,
    depth: A(t.depth),
    canvas: ar(e),
    context: A(t.context),
    stencil: A(t.stencil),
    antialias: A(t.antialias) ?? !0,
    precision: A(t.precision),
    powerPreference: A(t.powerPreference),
    premultipliedAlpha: A(t.premultipliedAlpha),
    preserveDrawingBuffer: A(t.preserveDrawingBuffer),
    logarithmicDepthBuffer: A(t.logarithmicDepthBuffer),
    failIfMajorPerformanceCaveat: A(t.failIfMajorPerformanceCaveat)
  })), a = H(new me(s.value));
  function c() {
    t.renderMode === "on-demand" && o();
  }
  ie(s, () => {
    a.value.dispose(), a.value = new me(s.value), c();
  }), ie([r.width, r.height], () => {
    a.value.setSize(r.width.value, r.height.value), c();
  }, {
    immediate: !0
  }), ie(() => t.clearColor, c);
  const { pixelRatio: i } = lr(), { logError: f } = Q(), u = (() => {
    const d = new me(), y = {
      shadowMap: {
        enabled: d.shadowMap.enabled,
        type: d.shadowMap.type
      },
      toneMapping: d.toneMapping,
      toneMappingExposure: d.toneMappingExposure,
      outputColorSpace: d.outputColorSpace
    };
    return d.dispose(), y;
  })(), _ = A(t.renderMode);
  return _ === "on-demand" && o(), _ === "manual" && setTimeout(() => {
    l();
  }, 100), Ae(() => {
    const d = A(t.preset);
    d && (d in oe || f(`Renderer Preset must be one of these: ${Object.keys(oe).join(", ")}`), gt(a.value, oe[d])), fn(a.value, i.value, A(t.dpr)), _ === "always" && (n.frames.value = Math.max(1, n.frames.value));
    const y = (P, C) => {
      const b = A(P), E = () => {
        if (d)
          return qe(oe[d], C);
      };
      if (b !== void 0)
        return b;
      const S = E();
      return S !== void 0 ? S : qe(u, C);
    }, p = (P, C) => en(a.value, C, y(P, C));
    p(t.shadows, "shadowMap.enabled"), p(t.toneMapping ?? ft, "toneMapping"), p(t.shadowMapType, "shadowMap.type"), bn < 150 && p(!t.useLegacyLights, "physicallyCorrectLights"), p(t.outputColorSpace, "outputColorSpace"), p(t.toneMappingExposure, "toneMappingExposure");
    const h = y(t.clearColor, "clearColor");
    h && a.value.setClearColor(
      h ? Mn(h) : new Z(0)
      // default clear color is not easily/efficiently retrievable from three
    );
  }), K(() => {
    a.value.dispose(), a.value.forceContextLoss();
  }), {
    renderer: a
  };
}
function Cn(e, t, r = 10) {
  const n = A(e) ? cr() : ur(q(() => A(t).parentElement)), o = _e(ze(n.width, r)), l = _e(ze(n.height, r)), s = q(() => o.value / l.value);
  return {
    height: l,
    width: o,
    aspectRatio: s
  };
}
const En = (e, t) => {
  const r = q(() => t.renderer.value.domElement), n = H([]), { x: o, y: l } = fr({ target: r });
  let s = 0;
  const { width: a, height: c, top: i, left: f } = pr(r), v = ({ x: g, y: T }) => {
    if (r.value)
      return {
        x: (g - f.value) / a.value * 2 - 1,
        y: -((T - i.value) / c.value) * 2 + 1
      };
  }, u = ({ x: g, y: T }) => {
    if (t.camera.value)
      return t.raycaster.value.setFromCamera(new ge(g, T), t.camera.value), n.value = t.raycaster.value.intersectObjects(e.value, !0), n.value;
  }, _ = (g) => {
    const T = v({
      x: (g == null ? void 0 : g.clientX) ?? o.value,
      y: (g == null ? void 0 : g.clientY) ?? l.value
    });
    return T ? u(T) || [] : [];
  }, d = D(), y = D(), p = D(), h = D(), P = D(), C = D(), b = D(), E = D();
  function S(g) {
    const T = {};
    for (const U in g)
      typeof U != "function" && (T[U] = g[U]);
    return T;
  }
  const M = (g, T) => {
    var Ie, $e, He;
    const U = S(T), re = new xe(T == null ? void 0 : T.clientX, T == null ? void 0 : T.clientY, 0).unproject((Ie = t.camera) == null ? void 0 : Ie.value);
    g.trigger({
      ...U,
      intersections: n.value,
      // The unprojectedPoint is wrong, math needs to be fixed
      unprojectedPoint: re,
      ray: ($e = t.raycaster) == null ? void 0 : $e.value.ray,
      camera: (He = t.camera) == null ? void 0 : He.value,
      sourceEvent: T,
      delta: s,
      stopPropagating: !1
    });
  };
  let x;
  const m = (g) => {
    _(g), M(p, g), x = g;
  }, w = () => {
    x && m(x);
  };
  let k, L, R;
  const B = (g) => {
    var T;
    k = (T = n.value[0]) == null ? void 0 : T.object, s = 0, L = new ge(
      (g == null ? void 0 : g.clientX) ?? o.value,
      (g == null ? void 0 : g.clientY) ?? l.value
    ), M(P, g);
  };
  let O, G = !1;
  const Oe = (g) => {
    var T, U, re;
    g instanceof PointerEvent && (n.value.length === 0 && M(C, g), k === ((T = n.value[0]) == null ? void 0 : T.object) && (R = new ge(
      (g == null ? void 0 : g.clientX) ?? o.value,
      (g == null ? void 0 : g.clientY) ?? l.value
    ), s = L == null ? void 0 : L.distanceTo(R), g.button === 0 ? (M(d, g), O === ((U = n.value[0]) == null ? void 0 : U.object) ? G = !0 : (O = (re = n.value[0]) == null ? void 0 : re.object, G = !1)) : g.button === 2 && M(b, g)), M(h, g));
  }, De = (g) => {
    G && (M(y, g), O = void 0, G = !1);
  }, je = (g) => M(p, g), Be = (g) => M(E, g);
  return r.value.addEventListener("pointerup", Oe), r.value.addEventListener("pointerdown", B), r.value.addEventListener("pointermove", m), r.value.addEventListener("pointerleave", je), r.value.addEventListener("dblclick", De), r.value.addEventListener("wheel", Be), K(() => {
    r != null && r.value && (r.value.removeEventListener("pointerup", Oe), r.value.removeEventListener("pointerdown", B), r.value.removeEventListener("pointermove", m), r.value.removeEventListener("pointerleave", je), r.value.removeEventListener("dblclick", De), r.value.removeEventListener("wheel", Be));
  }), {
    intersects: n,
    onClick: (g) => d.on(g).off,
    onDblClick: (g) => y.on(g).off,
    onContextMenu: (g) => b.on(g).off,
    onPointerMove: (g) => p.on(g).off,
    onPointerUp: (g) => h.on(g).off,
    onPointerDown: (g) => P.on(g).off,
    onPointerMissed: (g) => C.on(g).off,
    onWheel: (g) => E.on(g).off,
    forceUpdate: w
  };
};
function ye(e, t) {
  if (Array.isArray(e))
    for (const r of e)
      r(t);
  typeof e == "function" && e(t);
}
function Tn(e, t, r) {
  var x;
  const n = H(), o = H();
  e && (n.value = e), t && (o.value = t);
  const l = (m) => {
    var w;
    return ((w = m.__tres) == null ? void 0 : w.eventCount) > 0;
  }, s = (m) => {
    var w;
    return ((w = m.children) == null ? void 0 : w.some((k) => s(k))) || l(m);
  }, a = H(((x = n.value) == null ? void 0 : x.children).filter(s) || []);
  function c(m, w) {
    const k = [], L = () => w.stopPropagating = !0;
    w.stopPropagation = L;
    for (const R of w == null ? void 0 : w.intersections) {
      if (w.stopPropagating)
        return;
      w = { ...w, ...R };
      const { object: B } = R;
      w.eventObject = B, ye(B[m], w), k.push(B);
      let O = B.parent;
      for (; O !== null && !w.stopPropagating && !k.includes(O); )
        w.eventObject = O, ye(O[m], w), k.push(O), O = O.parent;
      const G = Xr(m.slice(2));
      r(G, { intersection: R, event: w });
    }
  }
  const {
    onClick: i,
    onDblClick: f,
    onContextMenu: v,
    onPointerMove: u,
    onPointerDown: _,
    onPointerUp: d,
    onPointerMissed: y,
    onWheel: p,
    forceUpdate: h
  } = En(a, t);
  d((m) => c("onPointerUp", m)), _((m) => c("onPointerDown", m)), i((m) => c("onClick", m)), f((m) => c("onDoubleClick", m)), v((m) => c("onContextMenu", m)), p((m) => c("onWheel", m));
  let P = [];
  u((m) => {
    const w = m.intersections.map(({ object: L }) => L), k = m.intersections;
    P.forEach(({ object: L }) => {
      w.includes(L) || (m.intersections = P, c("onPointerLeave", m), c("onPointerOut", m));
    }), m.intersections = k, m.intersections.forEach(({ object: L }) => {
      P.includes(L) || (c("onPointerEnter", m), c("onPointerOver", m));
    }), c("onPointerMove", m), P = m.intersections;
  });
  const C = [];
  y((m) => {
    const w = () => m.stopPropagating = !0;
    m.stopPropagation = w, C.forEach((k) => {
      m.stopPropagating || (m.eventObject = k, ye(k.onPointerMissed, m));
    }), r("pointer-missed", { event: m });
  });
  function b(m) {
    ne(m) && N(m) && a.value.push(m);
  }
  function E(m) {
    if (ne(m) && N(m)) {
      const w = a.value.indexOf(m);
      w > -1 && a.value.splice(w, 1);
    }
  }
  function S(m) {
    ne(m) && N(m) && m.onPointerMissed && C.push(m);
  }
  function M(m) {
    if (ne(m) && N(m)) {
      const w = C.indexOf(m);
      w > -1 && C.splice(w, 1);
    }
  }
  return t.eventManager = {
    forceUpdate: h,
    registerObject: b,
    deregisterObject: E,
    registerPointerMissedObject: S,
    deregisterPointerMissedObject: M
  }, {
    forceUpdate: h,
    registerObject: b,
    deregisterObject: E,
    registerPointerMissedObject: S,
    deregisterPointerMissedObject: M
  };
}
function Sn(e, t, r = 100) {
  r = r <= 0 ? 100 : r;
  const n = D(), o = /* @__PURE__ */ new Set();
  let l = !1, s = !1, a = null;
  function c() {
    a && clearTimeout(a), !s && !l && e() ? (n.trigger(t), o.forEach((u) => u()), o.clear(), l = !0) : !s && !l && (a = setTimeout(c, r));
  }
  function i() {
    s = !0, a && clearTimeout(a);
  }
  c();
  const f = (u, ..._) => {
    u(..._);
  };
  return {
    on: (u) => {
      if (l)
        return f(u, t), { off: () => {
        } };
      {
        const _ = n.on(u);
        return o.add(_.off), n.on(u);
      }
    },
    off: n.off,
    trigger: n.trigger,
    clear: n.clear,
    cancel: i
  };
}
const ee = /* @__PURE__ */ new WeakMap();
function bt(e) {
  if (e = e || pe(), ee.has(e))
    return ee.get(e);
  const t = 100, r = Date.now(), l = Sn(() => {
    if (Date.now() - r >= t)
      return !0;
    {
      const s = e.renderer.value, a = (s == null ? void 0 : s.domElement) || { width: 0, height: 0 };
      return !!(s && a.width > 0 && a.height > 0);
    }
  }, e);
  return ee.set(e, l), l;
}
function oo(e) {
  const t = pe();
  if (t)
    return ee.has(t) ? ee.get(t).on(e) : bt(t).on(e);
}
function An({
  scene: e,
  canvas: t,
  windowSize: r,
  rendererOptions: n,
  emit: o
}) {
  const l = H(e), s = Cn(r, t), {
    camera: a,
    cameras: c,
    registerCamera: i,
    deregisterCamera: f,
    setCameraActive: v
  } = mn({ sizes: s }), u = {
    mode: $(n.renderMode || "always"),
    priority: $(0),
    frames: $(0),
    maxFrames: 60,
    canBeInvalidated: q(() => u.mode.value === "on-demand" && u.frames.value === 0)
  };
  function _(R = 1) {
    n.renderMode === "on-demand" && (u.frames.value = Math.min(u.maxFrames, u.frames.value + R));
  }
  function d() {
    n.renderMode === "manual" && (u.frames.value = 1);
  }
  const { renderer: y } = Pn(
    {
      canvas: t,
      options: n,
      // TODO: replace contextParts with full ctx at https://github.com/Tresjs/tres/issues/516
      contextParts: { sizes: s, render: u, invalidate: _, advance: d }
    }
  ), p = {
    sizes: s,
    scene: l,
    camera: a,
    cameras: _e(c),
    renderer: y,
    raycaster: H(new qt()),
    controls: $(null),
    perf: {
      maxFrames: 160,
      fps: {
        value: 0,
        accumulator: []
      },
      memory: {
        currentMem: 0,
        allocatedMem: 0,
        accumulator: []
      }
    },
    render: u,
    advance: d,
    extend: Ee,
    invalidate: _,
    registerCamera: i,
    setCameraActive: v,
    deregisterCamera: f,
    loop: _n()
  };
  ae("useTres", p), p.scene.value.__tres = {
    root: p
  }, p.loop.register(() => {
    a.value && u.frames.value > 0 && (y.value.render(e, a.value), o("render", p.renderer.value)), u.priority.value = 0, u.mode.value === "always" ? u.frames.value = 1 : u.frames.value = Math.max(0, u.frames.value - 1);
  }, "render");
  const { on: h, cancel: P } = bt(p);
  p.loop.setReady(!1), p.loop.start(), h(() => {
    o("ready", p), p.loop.setReady(!0), Tn(e, p, o);
  }), K(() => {
    P(), p.loop.stop();
  });
  const C = 100, b = dr({ every: C }), { isSupported: E, memory: S } = mr({ interval: C }), M = 160;
  let x = performance.now();
  const m = ({ timestamp: R }) => {
    p.scene.value && (p.perf.memory.allocatedMem = Le(p.scene.value)), R - x >= C && (x = R, p.perf.fps.accumulator.push(b.value), p.perf.fps.accumulator.length > M && p.perf.fps.accumulator.shift(), p.perf.fps.value = b.value, E.value && S.value && (p.perf.memory.accumulator.push(S.value.usedJSHeapSize / 1024 / 1024), p.perf.memory.accumulator.length > M && p.perf.memory.accumulator.shift(), p.perf.memory.currentMem = p.perf.memory.accumulator.reduce((B, O) => B + O, 0) / p.perf.memory.accumulator.length));
  };
  let w = 0;
  const k = 1, { pause: L } = pt(({ delta: R }) => {
    window.__TRES__DEVTOOLS__ && (m({ timestamp: performance.now() }), w += R, w >= k && (window.__TRES__DEVTOOLS__.cb(p), w = 0));
  }, { immediate: !0 });
  return K(() => {
    L();
  }), p;
}
function pe() {
  const e = kt("useTres");
  if (!e)
    throw new Error("useTresContext must be used together with useTresContextProvider");
  return e;
}
const so = pe;
function io() {
  const {
    camera: e,
    scene: t,
    renderer: r,
    loop: n,
    raycaster: o,
    controls: l,
    invalidate: s,
    advance: a
  } = pe();
  n.setContext({
    camera: e,
    scene: t,
    renderer: r,
    raycaster: o,
    controls: l,
    invalidate: s,
    advance: a
  });
  function c(v, u = 0) {
    return n.register(v, "before", u);
  }
  function i(v) {
    return n.register(v, "render");
  }
  function f(v, u = 0) {
    return n.register(v, "after", u);
  }
  return {
    pause: n.pause,
    resume: n.resume,
    pauseRender: n.pauseRender,
    resumeRender: n.resumeRender,
    isActive: n.isActive,
    onBeforeRender: c,
    render: i,
    onAfterRender: f
  };
}
const Mt = D(), Pt = D(), Re = D(), te = new ut();
let le = 0, ce = 0;
const { pause: xn, resume: Xe, isActive: kn } = pt(
  () => {
    Mt.trigger({ delta: le, elapsed: ce, clock: te }), Pt.trigger({ delta: le, elapsed: ce, clock: te }), Re.trigger({ delta: le, elapsed: ce, clock: te });
  },
  { immediate: !1 }
);
Re.on(() => {
  le = te.getDelta(), ce = te.getElapsedTime();
});
let Ze = !1;
const ao = () => (Ze || (Ze = !0, Xe()), {
  onBeforeLoop: Mt.on,
  onLoop: Pt.on,
  onAfterLoop: Re.on,
  pause: xn,
  resume: Xe,
  isActive: kn
});
function lo() {
  const { logWarning: e } = Q();
  function t(l, s, a) {
    let c = null;
    return l.traverse((i) => {
      i[s] === a && (c = i);
    }), c || e(`Child with ${s} '${a}' not found.`), c;
  }
  function r(l, s, a) {
    const c = [];
    return l.traverse((i) => {
      i[s].includes(a) && c.push(i);
    }), c.length || e(`Children with ${s} '${a}' not found.`), c;
  }
  function n(l, s) {
    return t(l, "name", s);
  }
  function o(l, s) {
    return r(l, "name", s);
  }
  return {
    seek: t,
    seekByName: n,
    seekAll: r,
    seekAllByName: o
  };
}
function Ln(e, t = {}, r = {}) {
  let n = e;
  const o = (a) => {
    n = a;
  };
  let l = new Proxy({}, {});
  const s = {
    has(a, c) {
      return c in t || c in n;
    },
    get(a, c, i) {
      return c in t ? t[c](n) : n[c];
    },
    set(a, c, i) {
      return r[c] ? r[c](i, n, l, o) : n[c] = i, !0;
    }
  };
  return l = new Proxy({}, s), l;
}
const { logError: et } = Q(), tt = [
  "onClick",
  "onContextMenu",
  "onPointerMove",
  "onPointerEnter",
  "onPointerLeave",
  "onPointerOver",
  "onPointerOut",
  "onDoubleClick",
  "onPointerDown",
  "onPointerUp",
  "onPointerCancel",
  "onPointerMissed",
  "onLostPointerCapture",
  "onWheel"
], Rn = (e) => {
  const t = e.scene.value;
  function r(i, f, v, u) {
    if (u || (u = {}), u.args || (u.args = []), i === "template" || Jr(i))
      return null;
    let _ = i.replace("Tres", ""), d;
    if (i === "primitive") {
      (!j(u.object) || Lt(u.object)) && et(
        "Tres primitives need an 'object' prop, whose value is an object or shallowRef<object>"
      ), _ = u.object.type;
      const y = {};
      d = Ln(
        u.object,
        {
          object: (h) => h,
          isPrimitive: () => !0,
          __tres: () => y
        },
        {
          object: (h, P, C, b) => {
            pn(h, C, b, { patchProp: l, remove: o, insert: n }, e);
          },
          __tres: (h) => {
            Object.assign(y, h);
          }
        }
      );
    } else {
      const y = Ce.value[_];
      y || et(
        `${_} is not defined on the THREE namespace. Use extend to add it to the catalog.`
      ), d = new y(...u.args);
    }
    return d ? (d.isCamera && (u != null && u.position || d.position.set(3, 3, 3), u != null && u.lookAt || d.lookAt(0, 0, 0)), d = z(d, {
      ...d.__tres,
      type: _,
      memoizedProps: u,
      eventCount: 0,
      primitive: i === "primitive",
      attach: u.attach
    }, e), d) : null;
  }
  function n(i, f) {
    var _, d, y;
    if (!i)
      return;
    f = f || t;
    const v = i.__tres ? i : z(i, {}, e), u = f.__tres ? f : z(f, {}, e);
    i = J(v), f = J(u), i.__tres && ((_ = i.__tres) == null ? void 0 : _.eventCount) > 0 && ((d = e.eventManager) == null || d.registerObject(i)), e.registerCamera(i), (y = e.eventManager) == null || y.registerPointerMissedObject(i), v.__tres.attach ? cn(u, v, v.__tres.attach) : N(i) && N(u) && (u.add(i), i.dispatchEvent({ type: "added" })), v.__tres.parent = u, u.__tres.objects && !u.__tres.objects.includes(v) && u.__tres.objects.push(v);
  }
  function o(i, f) {
    var d, y, p, h;
    if (!i)
      return;
    i != null && i.__tres && ((d = i.__tres) == null ? void 0 : d.eventCount) > 0 && ((y = e.eventManager) == null || y.deregisterObject(i)), f = be(f) ? "default" : f;
    const v = (p = i.__tres) == null ? void 0 : p.dispose;
    be(v) || (v === null ? f = !1 : f = v);
    const u = (h = i.__tres) == null ? void 0 : h.primitive, _ = f === "default" ? !u : !!f;
    if (i.__tres && "objects" in i.__tres && [...i.__tres.objects].forEach((P) => o(P, f)), _ && i.children && [...i.children].forEach((P) => o(P, f)), _t(i, e), wt(i, e), _ && !Yr(i)) {
      if (Y(f))
        f(i);
      else if (Y(i.dispose))
        try {
          i.dispose();
        } catch {
        }
    }
    "__tres" in i && delete i.__tres;
  }
  function l(i, f, v, u) {
    var P, C;
    if (!i)
      return;
    let _ = i, d = f;
    if (i.__tres && (i.__tres.memoizedProps[f] = u), f === "attach") {
      const b = ((P = i.__tres) == null ? void 0 : P.parent) || i.parent;
      o(i), z(i, { attach: u }, e), b && n(i, b);
      return;
    }
    if (f === "dispose") {
      i.__tres || (i = z(i, {}, e)), i.__tres.dispose = u;
      return;
    }
    if (N(i) && d === "blocks-pointer-events") {
      u || u === "" ? i[d] = u : delete i[d];
      return;
    }
    tt.includes(f) && i.__tres && (i.__tres.eventCount += 1);
    let y = he(d), p = _ == null ? void 0 : _[y];
    if (d === "args") {
      const b = i, E = v ?? [], S = u ?? [], M = ((C = i.__tres) == null ? void 0 : C.type) || i.type;
      M && E.length && !tn(E, S) && (_ = Object.assign(
        b,
        new Ce.value[M](...u)
      ));
      return;
    }
    if (_.type === "BufferGeometry") {
      if (d === "args")
        return;
      _.setAttribute(
        he(d),
        new Jt(...u)
      );
      return;
    }
    if (d.includes("-") && p === void 0) {
      p = _;
      for (const b of d.split("-"))
        y = d = he(b), _ = p, p = p == null ? void 0 : p[d];
    }
    let h = u;
    if (h === "" && (h = !0), Y(p)) {
      tt.includes(f) || (ke(h) ? i[y](...h) : i[y](h)), y.startsWith("on") && Y(h) && (_[y] = h);
      return;
    }
    Ge(p) && Ge(h) ? p.mask = h.mask : mt(p) && Ur(h) ? p.set(h) : Fr(p) && Wr(h) && p.constructor === h.constructor ? p.copy(h) : Me(p) && Array.isArray(h) ? "fromArray" in p && typeof p.fromArray == "function" ? p.fromArray(h) : p.set(...h) : Me(p) && typeof h == "number" ? "setScalar" in p && typeof p.setScalar == "function" ? p.setScalar(h) : p.set(h) : _[y] = h, yt(i);
  }
  function s(i) {
    var f;
    return ((f = i == null ? void 0 : i.__tres) == null ? void 0 : f.parent) || null;
  }
  function a(i) {
    const f = z(new Kt(), { type: "Comment" }, e);
    return f.name = i, f;
  }
  function c(i) {
    var _;
    const f = s(i), v = ((_ = f == null ? void 0 : f.__tres) == null ? void 0 : _.objects) || [], u = v.indexOf(i);
    return u < 0 || u >= v.length - 1 ? null : v[u + 1];
  }
  return {
    insert: n,
    remove: o,
    createElement: r,
    patchProp: l,
    parentNode: s,
    createText: () => void 0,
    createComment: a,
    setText: () => void 0,
    setElementText: () => void 0,
    nextSibling: c,
    querySelector: () => void 0,
    setScopeId: () => void 0,
    cloneNode: () => void 0,
    insertStaticContent: () => void 0
  };
};
function On() {
  return Ct().__VUE_DEVTOOLS_GLOBAL_HOOK__;
}
function Ct() {
  return typeof navigator < "u" && typeof window < "u" ? window : typeof globalThis < "u" ? globalThis : {};
}
const Dn = typeof Proxy == "function", jn = "devtools-plugin:setup", Bn = "plugin:settings:set";
let V, Te;
function In() {
  var e;
  return V !== void 0 || (typeof window < "u" && window.performance ? (V = !0, Te = window.performance) : typeof globalThis < "u" && (!((e = globalThis.perf_hooks) === null || e === void 0) && e.performance) ? (V = !0, Te = globalThis.perf_hooks.performance) : V = !1), V;
}
function $n() {
  return In() ? Te.now() : Date.now();
}
class Hn {
  constructor(t, r) {
    this.target = null, this.targetQueue = [], this.onQueue = [], this.plugin = t, this.hook = r;
    const n = {};
    if (t.settings)
      for (const s in t.settings) {
        const a = t.settings[s];
        n[s] = a.defaultValue;
      }
    const o = `__vue-devtools-plugin-settings__${t.id}`;
    let l = Object.assign({}, n);
    try {
      const s = localStorage.getItem(o), a = JSON.parse(s);
      Object.assign(l, a);
    } catch {
    }
    this.fallbacks = {
      getSettings() {
        return l;
      },
      setSettings(s) {
        try {
          localStorage.setItem(o, JSON.stringify(s));
        } catch {
        }
        l = s;
      },
      now() {
        return $n();
      }
    }, r && r.on(Bn, (s, a) => {
      s === this.plugin.id && this.fallbacks.setSettings(a);
    }), this.proxiedOn = new Proxy({}, {
      get: (s, a) => this.target ? this.target.on[a] : (...c) => {
        this.onQueue.push({
          method: a,
          args: c
        });
      }
    }), this.proxiedTarget = new Proxy({}, {
      get: (s, a) => this.target ? this.target[a] : a === "on" ? this.proxiedOn : Object.keys(this.fallbacks).includes(a) ? (...c) => (this.targetQueue.push({
        method: a,
        args: c,
        resolve: () => {
        }
      }), this.fallbacks[a](...c)) : (...c) => new Promise((i) => {
        this.targetQueue.push({
          method: a,
          args: c,
          resolve: i
        });
      })
    });
  }
  async setRealTarget(t) {
    this.target = t;
    for (const r of this.onQueue)
      this.target.on[r.method](...r.args);
    for (const r of this.targetQueue)
      r.resolve(await this.target[r.method](...r.args));
  }
}
function Un(e, t) {
  const r = e, n = Ct(), o = On(), l = Dn && r.enableEarlyProxy;
  if (o && (n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !l))
    o.emit(jn, e, t);
  else {
    const s = l ? new Hn(r, o) : null;
    (n.__VUE_DEVTOOLS_PLUGINS__ = n.__VUE_DEVTOOLS_PLUGINS__ || []).push({
      pluginDescriptor: r,
      setupFn: t,
      proxy: s
    }), s && t(s.proxiedTarget);
  }
}
function Fn(e, t) {
  const r = `▲ ■ ●${e}`;
  typeof rt == "function" ? rt(r, t) : console.log(r);
}
function rt(e, t) {
  throw new Error(e + t);
}
const Et = (e) => {
  const t = {
    id: e.uuid,
    label: e.type,
    children: [],
    tags: []
  };
  e.name !== "" && t.tags.push({
    label: e.name,
    textColor: 5750629,
    backgroundColor: 15793395
  });
  const r = Le(e);
  return r > 0 && t.tags.push({
    label: `${wn(r)} KB`,
    textColor: 15707189,
    backgroundColor: 16775644,
    tooltip: "Memory usage"
  }), e.type.includes("Light") && (Gr(e) && t.tags.push({
    label: `${e.intensity}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Intensity"
  }), t.tags.push({
    label: `#${new Z(e.color).getHexString()}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Color"
  })), e.type.includes("Camera") && (t.tags.push({
    label: `${e.fov}°`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Field of view"
  }), t.tags.push({
    label: `x: ${Math.round(e.position.x)} y: ${Math.round(e.position.y)} z: ${Math.round(e.position.z)}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Position"
  })), t;
};
function Tt(e, t, r = "") {
  e.children.forEach((n) => {
    if (n.type === "HightlightMesh" || r && !n.type.includes(r) && !n.name.includes(r))
      return;
    const o = Et(n);
    t.children.push(o), Tt(n, o, r);
  });
}
const Wn = [], X = "tres:inspector", Nn = Se({
  sceneGraph: null
});
function zn(e, t) {
  Un(
    {
      id: "dev.esm.tres",
      label: "TresJS 🪐",
      logo: "https://raw.githubusercontent.com/Tresjs/tres/main/public/favicon.svg",
      packageName: "tresjs",
      homepage: "https://tresjs.org",
      componentStateTypes: Wn,
      app: e
    },
    (r) => {
      typeof r.now != "function" && Fn(
        "You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."
      ), r.addInspector({
        id: X,
        label: "TresJS 🪐",
        icon: "account_tree",
        treeFilterPlaceholder: "Search instances"
      }), setInterval(() => {
        r.sendInspectorTree(X);
      }, 1e3), setInterval(() => {
        r.notifyComponentUpdate();
      }, 5e3), r.on.getInspectorTree((l) => {
        if (l.inspectorId === X) {
          const s = Et(t.scene.value);
          Tt(t.scene.value, s, l.filter), Nn.sceneGraph = s, l.rootNodes = [s];
        }
      });
      let n = null, o = null;
      r.on.getInspectorState((l) => {
        var s;
        if (l.inspectorId === X) {
          const [a] = t.scene.value.getObjectsByProperty("uuid", l.nodeId);
          if (!a)
            return;
          if (o && n && n.parent && o.remove(n), a.isMesh) {
            const c = on(a);
            a.add(c), n = c, o = a;
          }
          l.state = {
            object: Object.entries(a).map(([c, i]) => c === "children" ? { key: c, value: i.filter((f) => f.type !== "HightlightMesh") } : { key: c, value: i, editable: !0 }).filter(({ key: c }) => c !== "parent")
          }, a.isScene && (l.state = {
            ...l.state,
            state: [
              {
                key: "Scene Info",
                value: {
                  objects: a.children.length,
                  memory: Le(a),
                  calls: t.renderer.value.info.render.calls,
                  triangles: t.renderer.value.info.render.triangles,
                  points: t.renderer.value.info.render.points,
                  lines: t.renderer.value.info.render.lines
                }
              },
              {
                key: "Programs",
                value: ((s = t.renderer.value.info.programs) == null ? void 0 : s.map((c) => ({
                  ...c,
                  programName: c.name
                }))) || []
              }
            ]
          });
        }
      }), r.on.editInspectorState((l) => {
        l.inspectorId === X && nn(t.scene.value, l.nodeId, l.path, l.state.value);
      });
    }
  );
}
const Gn = ["data-scene", "data-tres"], Vn = /* @__PURE__ */ ue({
  __name: "TresCanvas",
  props: {
    shadows: { type: Boolean, default: void 0 },
    clearColor: {},
    toneMapping: {},
    shadowMapType: {},
    useLegacyLights: { type: Boolean, default: void 0 },
    outputColorSpace: {},
    toneMappingExposure: {},
    renderMode: { default: "always" },
    dpr: {},
    camera: {},
    preset: {},
    windowSize: { type: Boolean, default: void 0 },
    enableProvideBridge: { type: Boolean, default: !0 },
    context: {},
    alpha: { type: Boolean, default: void 0 },
    premultipliedAlpha: { type: Boolean },
    antialias: { type: Boolean, default: void 0 },
    stencil: { type: Boolean, default: void 0 },
    preserveDrawingBuffer: { type: Boolean, default: void 0 },
    powerPreference: {},
    depth: { type: Boolean, default: void 0 },
    failIfMajorPerformanceCaveat: { type: Boolean, default: void 0 },
    precision: {},
    logarithmicDepthBuffer: { type: Boolean, default: void 0 },
    reverseDepthBuffer: { type: Boolean }
  },
  emits: [
    "render",
    "click",
    "double-click",
    "context-menu",
    "pointer-move",
    "pointer-up",
    "pointer-down",
    "pointer-enter",
    "pointer-leave",
    "pointer-over",
    "pointer-out",
    "pointer-missed",
    "wheel",
    "ready"
  ],
  setup(e, { expose: t, emit: r }) {
    const n = e, o = r, l = Rt(), s = $(), a = H(new at()), c = Ue();
    Ee(it);
    const i = (d, y = !1) => ue({
      setup() {
        var C;
        const p = (C = Ue()) == null ? void 0 : C.appContext;
        p && (p.app = c == null ? void 0 : c.appContext.app);
        const h = {};
        function P(b) {
          b && (b.parent && P(b.parent), b.provides && Object.assign(h, b.provides));
        }
        return c != null && c.parent && n.enableProvideBridge && (P(c.parent), Reflect.ownKeys(h).forEach((b) => {
          ae(b, h[b]);
        })), ae("useTres", d), ae("extend", Ee), typeof window < "u" && zn(p == null ? void 0 : p.app, d), () => Fe(Ht, null, y ? [] : l.default());
      }
    }), f = (d, y = !1) => {
      const p = i(d, y), { render: h } = $t(Rn(d));
      h(Fe(p), a.value);
    }, v = (d, y = !1) => {
      vt(d.scene.value), y && (d.renderer.value.dispose(), d.renderer.value.renderLists.dispose(), d.renderer.value.forceContextLoss()), a.value.__tres = {
        root: d
      };
    }, u = H(null);
    t({ context: u, dispose: () => v(u.value, !0) });
    const _ = () => {
      v(u.value), f(u.value, !0);
    };
    return Ot(() => {
      const d = s;
      u.value = An({
        scene: a.value,
        canvas: d,
        windowSize: n.windowSize ?? !1,
        rendererOptions: n,
        emit: o
      });
      const { registerCamera: y, camera: p, cameras: h, deregisterCamera: P } = u.value;
      f(u.value);
      const C = () => {
        const b = new we(
          45,
          window.innerWidth / window.innerHeight,
          0.1,
          1e3
        );
        b.position.set(3, 3, 3), b.lookAt(0, 0, 0), y(b);
        const E = Ae(() => {
          h.value.length >= 2 && (b.removeFromParent(), P(b), E == null || E());
        });
      };
      ie(
        () => n.camera,
        (b, E) => {
          b && y(b), E && (E.removeFromParent(), P(E));
        },
        {
          immediate: !0
        }
      ), p.value || C();
    }), K(_), (d, y) => (jt(), Dt("canvas", {
      ref_key: "canvas",
      ref: s,
      "data-scene": a.value.uuid,
      class: It(d.$attrs.class),
      "data-tres": `tresjs ${W(jr).version}`,
      style: Bt({
        display: "block",
        width: "100%",
        height: "100%",
        position: d.windowSize ? "fixed" : "relative",
        top: 0,
        left: 0,
        pointerEvents: "auto",
        touchAction: "none",
        ...d.$attrs.style
      })
    }, null, 14, Gn));
  }
}), Yn = [
  "TresCanvas",
  "TresLeches",
  "TresScene"
], co = {
  template: {
    compilerOptions: {
      isCustomElement: (e) => e.startsWith("Tres") && !Yn.includes(e) || e === "primitive"
    }
  }
}, { logWarning: qn } = Q();
let I = null;
const uo = {
  updated: (e, t) => {
    var o;
    const r = sn(t);
    if (!r) {
      qn(`v-distance-to: problem with binding value: ${t.value}`);
      return;
    }
    I && (I.dispose(), e.parent.remove(I));
    const n = r.clone().sub(e.position);
    n.normalize(), I = new Qt(n, e.position, e.position.distanceTo(r), 16776960), e.parent.add(I), console.table(
      [
        ["Distance:", e.position.distanceTo(r)],
        [`origin: ${e.name || e.type}`, `x:${e.position.x}, y:${e.position.y}, z:${(o = e.position) == null ? void 0 : o.z}`],
        [`Destiny: ${e.name || e.type}`, `x:${r.x}, y:${r.y}, z:${r == null ? void 0 : r.z}`]
      ]
    );
  },
  unmounted: (e) => {
    I == null || I.dispose(), e.parent && e.parent.remove(I);
  }
};
class St extends Xt {
  constructor(t, r) {
    const n = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0], o = new We();
    o.setAttribute("position", new Ne(n, 3)), o.computeBoundingSphere();
    const l = new Zt({ fog: !1 });
    super(o, l), this.light = t, this.color = r, this.type = "RectAreaLightHelper";
    const s = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0], a = new We();
    a.setAttribute("position", new Ne(s, 3)), a.computeBoundingSphere(), this.add(new er(a, new ct({ side: tr, fog: !1 })));
  }
  updateMatrixWorld() {
    if (this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1), this.color !== void 0)
      this.material.color.set(this.color), this.children[0].material.color.set(this.color);
    else {
      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity);
      const t = this.material.color, r = Math.max(t.r, t.g, t.b);
      r > 1 && t.multiplyScalar(1 / r), this.children[0].material.color.copy(this.material.color);
    }
    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld), this.children[0].matrixWorld.copy(this.matrixWorld);
  }
  dispose() {
    this.geometry.dispose(), this.material.dispose(), this.children[0].geometry.dispose(), this.children[0].material.dispose();
  }
}
const { logWarning: nt } = Q();
let se, F;
const Kn = {
  DirectionalLight: sr,
  PointLight: or,
  SpotLight: nr,
  HemisphereLight: rr,
  RectAreaLight: St
}, fo = {
  mounted: (e) => {
    if (!e.isLight) {
      nt(`${e.type} is not a light`);
      return;
    }
    se = Kn[e.type], e.parent.add(new se(e, 1, e.color.getHex()));
  },
  updated: (e) => {
    F = e.parent.children.find((t) => t instanceof se), !(F instanceof St) && F.update();
  },
  unmounted: (e) => {
    if (!e.isLight) {
      nt(`${e.type} is not a light`);
      return;
    }
    F = e.parent.children.find((t) => t instanceof se), F && F.dispose && F.dispose(), e.parent && e.parent.remove(F);
  }
}, po = {
  mounted: (e, t) => {
    if (t.arg) {
      console.log(`v-log:${t.arg}`, e[t.arg]);
      return;
    }
    console.log("v-log", e);
  }
}, mo = {
  install(e) {
    e.component("TresCanvas", Vn);
  }
};
export {
  Vn as TresCanvas,
  eo as UseLoader,
  to as UseTexture,
  Ce as catalogue,
  _n as createRenderLoop,
  mo as default,
  vt as dispose,
  Ee as extend,
  ro as isProd,
  Mn as normalizeColor,
  no as normalizeVectorFlexibleParam,
  oo as onTresReady,
  co as templateCompilerOptions,
  Br as traverseObjects,
  mn as useCamera,
  Ir as useLoader,
  Q as useLogger,
  io as useLoop,
  En as useRaycaster,
  ao as useRenderLoop,
  Pn as useRenderer,
  lo as useSeek,
  dn as useTexture,
  so as useTres,
  pe as useTresContext,
  An as useTresContextProvider,
  Tn as useTresEventManager,
  uo as vDistanceTo,
  fo as vLightHelper,
  po as vLog
};
