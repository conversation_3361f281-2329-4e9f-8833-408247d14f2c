/**
 * name: @tresjs/core
 * version: v4.3.6
 * (c) 2025
 * description: Declarative ThreeJS using Vue Components
 * author: <PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)
 */
(function(w,f){typeof exports=="object"&&typeof module<"u"?f(exports,require("vue"),require("three"),require("@vueuse/core")):typeof define=="function"&&define.amd?define(["exports","vue","three","@vueuse/core"],f):(w=typeof globalThis<"u"?globalThis:w||self,f(w.tres={},w.Vue,w.Three,w.VueUseCore))})(this,function(w,f,v,M){"use strict";var Dn=Object.defineProperty;var Tn=(w,f,v)=>f in w?Dn(w,f,{enumerable:!0,configurable:!0,writable:!0,value:v}):w[f]=v;var _e=(w,f,v)=>Tn(w,typeof f!="symbol"?f+"":f,v);function dt(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const n in e)if(n!=="default"){const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:()=>e[n]})}}return t.default=e,Object.freeze(t)}const we=dt(v),pt={name:"@tresjs/core",type:"module",version:"4.3.6",packageManager:"pnpm@10.6.3",description:"Declarative ThreeJS using Vue Components",author:"Alvaro Saburido <<EMAIL>> (https://github.com/alvarosabu/)",license:"MIT",repository:{type:"git",url:"git+https://github.com/Tresjs/tres.git"},keywords:["vue","3d","threejs","three","threejs-vue"],sideEffects:!1,exports:{".":{types:"./dist/index.d.ts",import:"./dist/tres.js",require:"./dist/tres.umd.cjs"},"./components":{types:"./dist/src/components/index.d.ts"},"./composables":{types:"./dist/src/composables/index.d.ts"},"./types":{types:"./dist/src/types/index.d.ts"},"./utils":{types:"./dist/src/utils/index.d.ts"},"./*":"./*"},main:"./dist/tres.js",module:"./dist/tres.js",types:"./dist/index.d.ts",files:["*.d.ts","dist"],publishConfig:{access:"public"},scripts:{dev:"pnpm --filter='./playground/vue' dev","dev:nuxt":"pnpm --filter='./playground/nuxt' dev",build:"vite build",test:"vitest","test:ci":"vitest run","test:ui":"vitest --ui --coverage.enabled=true",release:"release-it",coverage:"vitest run --coverage",lint:"eslint .","lint:fix":"eslint . --fix","docs:dev":"vitepress dev docs","docs:build":"vitepress build docs","docs:serve":"vitepress serve docs","docs:preview":"vitepress preview docs","docs:contributors":"esno scripts/update-contributors.ts"},peerDependencies:{three:">=0.133",vue:">=3.4"},dependencies:{"@alvarosabu/utils":"^3.2.0","@vue/devtools-api":"^6.6.3","@vueuse/core":"^12.5.0"},devDependencies:{"@release-it/conventional-changelog":"^10.0.0","@stackblitz/sdk":"^1.11.0","@tresjs/cientos":"4.1.0","@tresjs/eslint-config":"^1.4.0","@types/three":"^0.173.0","@typescript-eslint/eslint-plugin":"^8.23.0","@typescript-eslint/parser":"^8.23.0","@vitejs/plugin-vue":"^5.2.1","@vitest/coverage-c8":"^0.33.0","@vitest/coverage-v8":"^3.0.5","@vitest/ui":"^3.0.5","@vue/test-utils":"^2.4.6",eslint:"^9.19.0","eslint-plugin-vue":"^9.32.0",esno:"^4.8.0",gsap:"^3.12.7",jsdom:"^26.0.0",kolorist:"^1.8.0",ohmyfetch:"^0.4.21",pathe:"^2.0.2","release-it":"^18.1.2","rollup-plugin-analyzer":"^4.0.0","rollup-plugin-copy":"^3.5.0","rollup-plugin-visualizer":"^5.14.0",sponsorkit:"^16.3.0",three:"^0.173.0",unocss:"^65.4.3",unplugin:"^2.1.2","unplugin-vue-components":"^28.0.0",vite:"^6.1.0","vite-plugin-banner":"^0.8.0","vite-plugin-dts":"4.5.0","vite-plugin-inspect":"^10.1.0","vite-plugin-require-transform":"^1.0.21","vite-svg-loader":"^5.1.0",vitepress:"1.6.3",vitest:"3.0.5",vue:"3.5.13","vue-demi":"^0.14.10"}};function be(e){const t={nodes:{},materials:{}};return e&&e.traverse(n=>{n.name&&(t.nodes[n.name]=n),n.material&&!t.materials[n.material.name]&&(t.materials[n.material.name]=n.material)}),t}async function Ce(e,t,n,r,o){const{logError:l}=z(),s=new e;return o&&o(s),n&&n(s),await new Promise((a,c)=>{s.load(t,i=>{const d=i;d.scene&&Object.assign(d,be(d.scene)),a(d)},r,i=>{l("[useLoader] - Failed to load resource",i),c(i)})})}const mt=f.defineComponent({__name:"component",props:{loader:{},url:{}},async setup(e){let t,n;const r=e,o=([t,n]=f.withAsyncContext(()=>f.reactive(Ce(r.loader,r.url))),t=await t,n(),t);return(l,s)=>f.renderSlot(l.$slots,"default",{data:f.unref(o)})}});class ht extends we.Mesh{constructor(...n){super(...n);_e(this,"type","HightlightMesh");_e(this,"createTime");this.createTime=Date.now()}onBeforeRender(){const r=(Date.now()-this.createTime)/1e3,s=1+.07*Math.sin(2.5*r);this.scale.set(s,s,s)}}function le(e){return typeof e>"u"}function ce(e){return Array.isArray(e)}function gt(e){return typeof e=="number"}function Me(e){return typeof e=="string"}function G(e){return typeof e=="function"}function R(e){return e===Object(e)&&!ce(e)&&!G(e)}function V(e){return R(e)&&!!e.isObject3D}function Pe(e){return R(e)&&!!e.isColor}function vt(e){return e!=null&&(typeof e=="string"||typeof e=="number"||Pe(e))}function ue(e){return e!==null&&typeof e=="object"&&"set"in e&&typeof e.set=="function"}function yt(e){return ue(e)&&"copy"in e&&typeof e.copy=="function"}function _t(e){return!!(e!=null&&e.constructor)}function Se(e){return e instanceof v.Layers}function ke(e){return R(e)&&!!e.isCamera}function wt(e){return R(e)&&!!e.isBufferGeometry}function bt(e){return R(e)&&!!e.isMaterial}function Ct(e){return R(e)&&!!e.isLight}function Mt(e){return R(e)&&!!e.isFog}function Pt(e){return R(e)&&!!e.isScene}function Z(e){return V(e)||wt(e)||bt(e)||Mt(e)}function St(e){return R(e)&&!!e.isPrimitive}const Ae=(e,t)=>{for(const n of Object.keys(t))t[n]instanceof Object&&Object.assign(t[n],Ae(e[n],t[n]));return Object.assign(e||{},t),e},kt=Ot("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot");function Le(e){return e&&e.nodeType===1}function fe(e){return e.replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}const At=/\B([A-Z])/g;function Lt(e){return e.replace(At,"-$1").toLowerCase()}function Ot(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?o=>!!n[o.toLowerCase()]:o=>!!n[o]}const Oe=(e,t)=>{if(!t)return;const n=Array.isArray(t)?t:t.match(/([^[.\]])+/g);return n==null?void 0:n.reduce((r,o)=>r&&r[o],e)},Dt=(e,t,n)=>{const r=Array.isArray(t)?t:t.match(/([^[.\]])+/g);r&&r.reduce((o,l,s)=>(o[l]===void 0&&(o[l]={}),s===r.length-1&&(o[l]=n),o[l]),e)};function De(e,t){if(Le(e)&&Le(t)){const o=e.attributes,l=t.attributes;return o.length!==l.length?!1:Array.from(o).every(({name:s,value:a})=>t.getAttribute(s)===a)}if(e===t)return!0;if(e===null||typeof e!="object"||t===null||typeof t!="object")return!1;const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const o of n)if(!r.includes(o)||!De(e[o],t[o]))return!1;return!0}function Tt(e,t){if(!Array.isArray(e)||!Array.isArray(t)||e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!De(e[n],t[n]))return!1;return!0}const xt=Array.isArray;function jt(e,t,n,r){const o=c=>{if(c.uuid===t)return c;for(const i of c.children){const d=o(i);if(d)return d}},l=o(e);if(!l){console.warn("Object with UUID not found in the scene.");return}let s=l;for(let c=0;c<n.length-1;c++)if(s[n[c]]!==void 0)s=s[n[c]];else{console.warn(`Property path is not valid: ${n.join(".")}`);return}const a=n[n.length-1];s[a]!==void 0?s[a]=r:console.warn(`Property path is not valid: ${n.join(".")}`)}function Bt(e){const t=new v.MeshBasicMaterial({color:11003607,transparent:!0,opacity:.2,depthTest:!1,side:v.DoubleSide});return new ht(e.geometry.clone(),t)}function It(e){var n;let t=e.value;return e.value&&((n=e.value)!=null&&n.isMesh)&&(t=e.value.position),Array.isArray(e.value)&&(t=new v.Vector3(...t)),t}function $t(e){return"map"in e}function Te(e){$t(e)&&e.map&&e.map.dispose(),e.dispose()}function de(e){var n,r;if(e.parent&&((n=e.removeFromParent)==null||n.call(e)),delete e.__tres,[...e.children].forEach(o=>de(o)),!(e instanceof v.Scene)){const o=e;e&&((r=e.dispose)==null||r.call(e)),o.geometry&&o.geometry.dispose(),Array.isArray(o.material)?o.material.forEach(l=>Te(l)):o.material&&Te(o.material)}}function Rt(e,t){let n=0;for(let r=0;r<e.length;r++)t(e[r],r)&&(e[n]=e[r],n++);return e.length=n,e}function pe(e,t){let n=e;if(t.includes("-")){const r=t.split("-");let o=r.shift();for(;n&&r.length;)o in n?(n=n[o],o=r.shift()):o=xe(o,r.shift());return{target:n,key:xe(o,...r)}}else return{target:n,key:t}}function xe(...e){return e.map((t,n)=>n===0?t:t.charAt(0).toUpperCase()+t.slice(1)).join("")}const je=/-\d+$/;function Et(e,t,n){if(Me(n)){if(je.test(n)){const l=n.replace(je,""),{target:s,key:a}=pe(e,l);if(!Array.isArray(s[a])){const c=s[a],i=[];i.__tresDetach=()=>{i.every(d=>le(d))&&(s[a]=c)},s[a]=i}}const{target:r,key:o}=pe(e,n);t.__tres.previousAttach=r[o],r[o]=q(t)}else t.__tres.previousAttach=n(e,t)}function Ut(e,t,n){var r,o,l;if(Me(n)){const{target:s,key:a}=pe(e,n),c=t.__tres.previousAttach;c===void 0?delete s[a]:s[a]=c,"__tresDetach"in s&&s.__tresDetach()}else(o=(r=t.__tres)==null?void 0:r.previousAttach)==null||o.call(r,e,t);(l=t.__tres)==null||delete l.previousAttach}function N(e,t,n){const r=e;return r.__tres={type:"unknown",eventCount:0,root:n,handlers:{},memoizedProps:{},objects:[],parent:null,previousAttach:null,...t},r.__tres.attach||(r.isMaterial?r.__tres.attach="material":r.isBufferGeometry?r.__tres.attach="geometry":r.isFog&&(r.__tres.attach="fog")),r}function Be(e){var n;const t=(n=e==null?void 0:e.__tres)==null?void 0:n.root;t&&t.render&&t.render.canBeInvalidated.value&&t.invalidate()}function Zn(e){}function Vt(e,t,n){var o;if(!G(e.setPixelRatio))return;let r=0;if(n&&ce(n)&&n.length>=2){const[l,s]=n;r=v.MathUtils.clamp(t,l,s)}else gt(n)?r=n:r=t;r!==((o=e.getPixelRatio)==null?void 0:o.call(e))&&e.setPixelRatio(r)}function Ft(e,t,n,r,o){const l=[...t.__tres.objects],s=q(t);if(e=q(e),s===e)return!0;const a=N(e,t.__tres??{},o),c=t.parent??t.__tres.parent??null,i={...t.__tres.memoizedProps};delete i.object;for(const d of l)Ie(d,o),$e(d,o);s.__tres.objects=[],r.remove(t);for(const[d,_]of Object.entries(i))r.patchProp(a,d,a[d],_);n(e),r.insert(t,c);for(const d of l)r.insert(d,t);return!0}function q(e){return St(e)?(e.object.__tres=e.__tres,e.object):e}function Ie(e,t){var r,o,l,s;const n=((r=e.__tres)==null?void 0:r.parent)||t.scene.value;e.__tres&&(e.__tres.parent=null),n&&n.__tres&&"objects"in n.__tres&&Rt(n.__tres.objects,a=>a!==e),(o=e.__tres)!=null&&o.attach?Ut(n,e,e.__tres.attach):((s=(l=e.parent)==null?void 0:l.remove)==null||s.call(l,q(e)),e.parent=null)}function $e(e,t){var n;(n=e.traverse)==null||n.call(e,r=>{var o;t.deregisterCamera(r),(o=t.eventManager)==null||o.deregisterPointerMissedObject(r)}),t.deregisterCamera(e),Be(e)}async function Re(e,t){const n=new v.TextureLoader(t),r=o=>new Promise((l,s)=>{n.load(o,a=>l(a),()=>null,()=>{s(new Error("[useTextures] - Failed to load texture"))})});if(xt(e)){const o=await Promise.all(e.map(l=>r(l)));return e.length>1?o:o[0]}else{const{map:o,displacementMap:l,normalMap:s,roughnessMap:a,metalnessMap:c,aoMap:i,alphaMap:d,matcap:_}=e;return{map:o?await r(o):null,displacementMap:l?await r(l):null,normalMap:s?await r(s):null,roughnessMap:a?await r(a):null,metalnessMap:c?await r(c):null,aoMap:i?await r(i):null,alphaMap:d?await r(d):null,matcap:_?await r(_):null}}}const Wt=f.defineComponent({__name:"component",props:{map:{},displacementMap:{},normalMap:{},roughnessMap:{},metalnessMap:{},aoMap:{},alphaMap:{},matcap:{}},async setup(e){let t,n;const r=e,o=([t,n]=f.withAsyncContext(()=>f.reactive(Re(r))),t=await t,n(),t);return(l,s)=>f.renderSlot(l.$slots,"default",{textures:f.unref(o)})}}),Ee=({sizes:e})=>{const t=f.ref([]),n=f.computed(()=>t.value[0]),r=s=>{const a=s instanceof v.Camera?s:t.value.find(i=>i.uuid===s);if(!a)return;const c=t.value.filter(({uuid:i})=>i!==a.uuid);t.value=[a,...c]},o=(s,a=!1)=>{if(ke(s)){const c=s;if(t.value.some(({uuid:i})=>i===c.uuid))return;a?r(c):t.value.push(c)}},l=s=>{if(ke(s)){const a=s;t.value=t.value.filter(({uuid:c})=>c!==a.uuid)}};return f.watchEffect(()=>{e.aspectRatio.value&&t.value.forEach(s=>{!s.manual&&(s instanceof v.PerspectiveCamera||Nt(s))&&(s instanceof v.PerspectiveCamera?s.aspect=e.aspectRatio.value:(s.left=e.width.value*-.5,s.right=e.width.value*.5,s.top=e.height.value*.5,s.bottom=e.height.value*-.5),s.updateProjectionMatrix())})}),f.onUnmounted(()=>{t.value=[]}),{camera:n,cameras:t,registerCamera:o,deregisterCamera:l,setCameraActive:r}};function Nt(e){return e.hasOwnProperty("isOrthographicCamera")&&e.isOrthographicCamera}const zt=!0,ee="[TresJS ▲ ■ ●] ";function Gt(...e){typeof e[0]=="string"?e[0]=ee+e[0]:e.unshift(ee),console.error(...e)}function qt(...e){typeof e[0]=="string"?e[0]=ee+e[0]:e.unshift(ee),console.warn(...e)}function Yt(e,t){}function z(){return{logError:Gt,logWarning:qt,logMessage:Yt}}const te=f.ref({}),ne=e=>Object.assign(te.value,e);function me(){const e=new Map,t=new Set;let n=0,r=!1;const o=()=>{const i=Array.from(e.entries()).sort((d,_)=>{const u=d[1].priority-_[1].priority;return u===0?d[1].addI-_[1].addI:u});t.clear(),i.forEach(d=>t.add(d[0]))},l=i=>{e.delete(i),t.delete(i)};return{on:(i,d=0)=>{e.set(i,{priority:d,addI:n++});const _=()=>l(i);return M.tryOnScopeDispose(_),r=!0,{off:_}},off:l,trigger:(...i)=>{r&&(o(),r=!1),t.forEach(d=>d(...i))},dispose:()=>{e.clear(),t.clear()},get count(){return e.size}}}function Ue(){let e=!0,t=!0,n=!1;const r=new v.Clock(!1),o=f.ref(r.running),l=f.ref(!1);let s;const a=v.MathUtils.generateUUID();let c=null;const i=me(),d=me(),_=me();T();let u={};function C(k){u=k}function m(k,x,h=0){switch(x){case"before":return i.on(k,h);case"render":return c||(c=k),d.dispose(),d.on(k);case"after":return _.on(k,h)}}function b(){t&&(t=!1,T(),O())}function p(){t=!0,T(),cancelAnimationFrame(s)}function y(){n=!1,T()}function A(){n=!0,T()}function L(){l.value=!0}function S(){l.value=!1}function O(){if(!e){s=requestAnimationFrame(O);return}const k=r.getDelta(),x=r.getElapsedTime(),h={camera:f.unref(u.camera),scene:f.unref(u.scene),renderer:f.unref(u.renderer),raycaster:f.unref(u.raycaster),controls:f.unref(u.controls),invalidate:u.invalidate,advance:u.advance},P={delta:k,elapsed:x,clock:r,...h};o.value&&i.trigger(P),l.value||(d.count?d.trigger(P):c&&c(P)),o.value&&_.trigger(P),s=requestAnimationFrame(O)}function T(){const k=!t&&!n;r.running!==k&&(r.running?r.stop():r.start()),o.value=r.running}return{loopId:a,register:(k,x,h)=>m(k,x,h),start:b,stop:p,pause:A,resume:y,pauseRender:L,resumeRender:S,isRenderPaused:l,isActive:o,setContext:C,setReady:k=>e=k}}function he(e){let t=0;return e.traverse(n=>{if(n.isMesh&&n.geometry&&n.type!=="HightlightMesh"){const r=n.geometry,o=r.attributes.position.count*3*Float32Array.BYTES_PER_ELEMENT,l=r.index?r.index.count*Uint32Array.BYTES_PER_ELEMENT:0,s=r.attributes.normal?r.attributes.normal.count*3*Float32Array.BYTES_PER_ELEMENT:0,a=r.attributes.uv?r.attributes.uv.count*2*Float32Array.BYTES_PER_ELEMENT:0,c=o+l+s+a;t+=c}}),t}function Kt(e){return(e/1024).toFixed(2)}const Jt=Number.parseInt(v.REVISION.replace("dev",""));function Qt(e){return typeof e=="number"?[e,e,e]:e instanceof v.Vector3?[e.x,e.y,e.z]:e}function Ve(e){return e instanceof v.Color?e:Array.isArray(e)?new v.Color(...e):new v.Color(e)}const re={realistic:{shadows:!0,physicallyCorrectLights:!0,outputColorSpace:v.SRGBColorSpace,toneMapping:v.ACESFilmicToneMapping,toneMappingExposure:3,shadowMap:{enabled:!0,type:v.PCFSoftShadowMap}},flat:{toneMapping:v.NoToneMapping,toneMappingExposure:1}};function Fe({canvas:e,options:t,contextParts:{sizes:n,render:r,invalidate:o,advance:l}}){const s=f.computed(()=>({alpha:M.toValue(t.alpha)??!0,depth:M.toValue(t.depth),canvas:M.unrefElement(e),context:M.toValue(t.context),stencil:M.toValue(t.stencil),antialias:M.toValue(t.antialias)??!0,precision:M.toValue(t.precision),powerPreference:M.toValue(t.powerPreference),premultipliedAlpha:M.toValue(t.premultipliedAlpha),preserveDrawingBuffer:M.toValue(t.preserveDrawingBuffer),logarithmicDepthBuffer:M.toValue(t.logarithmicDepthBuffer),failIfMajorPerformanceCaveat:M.toValue(t.failIfMajorPerformanceCaveat)})),a=f.shallowRef(new v.WebGLRenderer(s.value));function c(){t.renderMode==="on-demand"&&o()}f.watch(s,()=>{a.value.dispose(),a.value=new v.WebGLRenderer(s.value),c()}),f.watch([n.width,n.height],()=>{a.value.setSize(n.width.value,n.height.value),c()},{immediate:!0}),f.watch(()=>t.clearColor,c);const{pixelRatio:i}=M.useDevicePixelRatio(),{logError:d}=z(),u=(()=>{const m=new v.WebGLRenderer,b={shadowMap:{enabled:m.shadowMap.enabled,type:m.shadowMap.type},toneMapping:m.toneMapping,toneMappingExposure:m.toneMappingExposure,outputColorSpace:m.outputColorSpace};return m.dispose(),b})(),C=M.toValue(t.renderMode);return C==="on-demand"&&o(),C==="manual"&&setTimeout(()=>{l()},100),f.watchEffect(()=>{const m=M.toValue(t.preset);m&&(m in re||d(`Renderer Preset must be one of these: ${Object.keys(re).join(", ")}`),Ae(a.value,re[m])),Vt(a.value,i.value,M.toValue(t.dpr)),C==="always"&&(r.frames.value=Math.max(1,r.frames.value));const b=(A,L)=>{const S=M.toValue(A),O=()=>{if(m)return Oe(re[m],L)};if(S!==void 0)return S;const T=O();return T!==void 0?T:Oe(u,L)},p=(A,L)=>Dt(a.value,L,b(A,L));p(t.shadows,"shadowMap.enabled"),p(t.toneMapping??v.ACESFilmicToneMapping,"toneMapping"),p(t.shadowMapType,"shadowMap.type"),Jt<150&&p(!t.useLegacyLights,"physicallyCorrectLights"),p(t.outputColorSpace,"outputColorSpace"),p(t.toneMappingExposure,"toneMappingExposure");const y=b(t.clearColor,"clearColor");y&&a.value.setClearColor(y?Ve(y):new v.Color(0))}),f.onUnmounted(()=>{a.value.dispose(),a.value.forceContextLoss()}),{renderer:a}}function Xt(e,t,n=10){const r=M.toValue(e)?M.useWindowSize():M.useElementSize(f.computed(()=>M.toValue(t).parentElement)),o=f.readonly(M.refDebounced(r.width,n)),l=f.readonly(M.refDebounced(r.height,n)),s=f.computed(()=>o.value/l.value);return{height:l,width:o,aspectRatio:s}}const We=(e,t)=>{const n=f.computed(()=>t.renderer.value.domElement),r=f.shallowRef([]),{x:o,y:l}=M.usePointer({target:n});let s=0;const{width:a,height:c,top:i,left:d}=M.useElementBounding(n),_=({x:g,y:D})=>{if(n.value)return{x:(g-d.value)/a.value*2-1,y:-((D-i.value)/c.value)*2+1}},u=({x:g,y:D})=>{if(t.camera.value)return t.raycaster.value.setFromCamera(new v.Vector2(g,D),t.camera.value),r.value=t.raycaster.value.intersectObjects(e.value,!0),r.value},C=g=>{const D=_({x:(g==null?void 0:g.clientX)??o.value,y:(g==null?void 0:g.clientY)??l.value});return D?u(D)||[]:[]},m=M.createEventHook(),b=M.createEventHook(),p=M.createEventHook(),y=M.createEventHook(),A=M.createEventHook(),L=M.createEventHook(),S=M.createEventHook(),O=M.createEventHook();function T(g){const D={};for(const W in g)typeof W!="function"&&(D[W]=g[W]);return D}const k=(g,D)=>{var ct,ut,ft;const W=T(D),ae=new v.Vector3(D==null?void 0:D.clientX,D==null?void 0:D.clientY,0).unproject((ct=t.camera)==null?void 0:ct.value);g.trigger({...W,intersections:r.value,unprojectedPoint:ae,ray:(ut=t.raycaster)==null?void 0:ut.value.ray,camera:(ft=t.camera)==null?void 0:ft.value,sourceEvent:D,delta:s,stopPropagating:!1})};let x;const h=g=>{C(g),k(p,g),x=g},P=()=>{x&&h(x)};let j,B,I;const U=g=>{var D;j=(D=r.value[0])==null?void 0:D.object,s=0,B=new v.Vector2((g==null?void 0:g.clientX)??o.value,(g==null?void 0:g.clientY)??l.value),k(A,g)};let $,K=!1;const st=g=>{var D,W,ae;g instanceof PointerEvent&&(r.value.length===0&&k(L,g),j===((D=r.value[0])==null?void 0:D.object)&&(I=new v.Vector2((g==null?void 0:g.clientX)??o.value,(g==null?void 0:g.clientY)??l.value),s=B==null?void 0:B.distanceTo(I),g.button===0?(k(m,g),$===((W=r.value[0])==null?void 0:W.object)?K=!0:($=(ae=r.value[0])==null?void 0:ae.object,K=!1)):g.button===2&&k(S,g)),k(y,g))},it=g=>{K&&(k(b,g),$=void 0,K=!1)},at=g=>k(p,g),lt=g=>k(O,g);return n.value.addEventListener("pointerup",st),n.value.addEventListener("pointerdown",U),n.value.addEventListener("pointermove",h),n.value.addEventListener("pointerleave",at),n.value.addEventListener("dblclick",it),n.value.addEventListener("wheel",lt),f.onUnmounted(()=>{n!=null&&n.value&&(n.value.removeEventListener("pointerup",st),n.value.removeEventListener("pointerdown",U),n.value.removeEventListener("pointermove",h),n.value.removeEventListener("pointerleave",at),n.value.removeEventListener("dblclick",it),n.value.removeEventListener("wheel",lt))}),{intersects:r,onClick:g=>m.on(g).off,onDblClick:g=>b.on(g).off,onContextMenu:g=>S.on(g).off,onPointerMove:g=>p.on(g).off,onPointerUp:g=>y.on(g).off,onPointerDown:g=>A.on(g).off,onPointerMissed:g=>L.on(g).off,onWheel:g=>O.on(g).off,forceUpdate:P}};function ge(e,t){if(Array.isArray(e))for(const n of e)n(t);typeof e=="function"&&e(t)}function Ne(e,t,n){var x;const r=f.shallowRef(),o=f.shallowRef();e&&(r.value=e),t&&(o.value=t);const l=h=>{var P;return((P=h.__tres)==null?void 0:P.eventCount)>0},s=h=>{var P;return((P=h.children)==null?void 0:P.some(j=>s(j)))||l(h)},a=f.shallowRef(((x=r.value)==null?void 0:x.children).filter(s)||[]);function c(h,P){const j=[],B=()=>P.stopPropagating=!0;P.stopPropagation=B;for(const I of P==null?void 0:P.intersections){if(P.stopPropagating)return;P={...P,...I};const{object:U}=I;P.eventObject=U,ge(U[h],P),j.push(U);let $=U.parent;for(;$!==null&&!P.stopPropagating&&!j.includes($);)P.eventObject=$,ge($[h],P),j.push($),$=$.parent;const K=Lt(h.slice(2));n(K,{intersection:I,event:P})}}const{onClick:i,onDblClick:d,onContextMenu:_,onPointerMove:u,onPointerDown:C,onPointerUp:m,onPointerMissed:b,onWheel:p,forceUpdate:y}=We(a,t);m(h=>c("onPointerUp",h)),C(h=>c("onPointerDown",h)),i(h=>c("onClick",h)),d(h=>c("onDoubleClick",h)),_(h=>c("onContextMenu",h)),p(h=>c("onWheel",h));let A=[];u(h=>{const P=h.intersections.map(({object:B})=>B),j=h.intersections;A.forEach(({object:B})=>{P.includes(B)||(h.intersections=A,c("onPointerLeave",h),c("onPointerOut",h))}),h.intersections=j,h.intersections.forEach(({object:B})=>{A.includes(B)||(c("onPointerEnter",h),c("onPointerOver",h))}),c("onPointerMove",h),A=h.intersections});const L=[];b(h=>{const P=()=>h.stopPropagating=!0;h.stopPropagation=P,L.forEach(j=>{h.stopPropagating||(h.eventObject=j,ge(j.onPointerMissed,h))}),n("pointer-missed",{event:h})});function S(h){Z(h)&&V(h)&&a.value.push(h)}function O(h){if(Z(h)&&V(h)){const P=a.value.indexOf(h);P>-1&&a.value.splice(P,1)}}function T(h){Z(h)&&V(h)&&h.onPointerMissed&&L.push(h)}function k(h){if(Z(h)&&V(h)){const P=L.indexOf(h);P>-1&&L.splice(P,1)}}return t.eventManager={forceUpdate:y,registerObject:S,deregisterObject:O,registerPointerMissedObject:T,deregisterPointerMissedObject:k},{forceUpdate:y,registerObject:S,deregisterObject:O,registerPointerMissedObject:T,deregisterPointerMissedObject:k}}function Ht(e,t,n=100){n=n<=0?100:n;const r=M.createEventHook(),o=new Set;let l=!1,s=!1,a=null;function c(){a&&clearTimeout(a),!s&&!l&&e()?(r.trigger(t),o.forEach(u=>u()),o.clear(),l=!0):!s&&!l&&(a=setTimeout(c,n))}function i(){s=!0,a&&clearTimeout(a)}c();const d=(u,...C)=>{u(...C)};return{on:u=>{if(l)return d(u,t),{off:()=>{}};{const C=r.on(u);return o.add(C.off),r.on(u)}},off:r.off,trigger:r.trigger,clear:r.clear,cancel:i}}const J=new WeakMap;function ze(e){if(e=e||Q(),J.has(e))return J.get(e);const t=100,n=Date.now(),l=Ht(()=>{if(Date.now()-n>=t)return!0;{const s=e.renderer.value,a=(s==null?void 0:s.domElement)||{width:0,height:0};return!!(s&&a.width>0&&a.height>0)}},e);return J.set(e,l),l}function Zt(e){const t=Q();if(t)return J.has(t)?J.get(t).on(e):ze(t).on(e)}function Ge({scene:e,canvas:t,windowSize:n,rendererOptions:r,emit:o}){const l=f.shallowRef(e),s=Xt(n,t),{camera:a,cameras:c,registerCamera:i,deregisterCamera:d,setCameraActive:_}=Ee({sizes:s}),u={mode:f.ref(r.renderMode||"always"),priority:f.ref(0),frames:f.ref(0),maxFrames:60,canBeInvalidated:f.computed(()=>u.mode.value==="on-demand"&&u.frames.value===0)};function C(I=1){r.renderMode==="on-demand"&&(u.frames.value=Math.min(u.maxFrames,u.frames.value+I))}function m(){r.renderMode==="manual"&&(u.frames.value=1)}const{renderer:b}=Fe({canvas:t,options:r,contextParts:{sizes:s,render:u,invalidate:C,advance:m}}),p={sizes:s,scene:l,camera:a,cameras:f.readonly(c),renderer:b,raycaster:f.shallowRef(new v.Raycaster),controls:f.ref(null),perf:{maxFrames:160,fps:{value:0,accumulator:[]},memory:{currentMem:0,allocatedMem:0,accumulator:[]}},render:u,advance:m,extend:ne,invalidate:C,registerCamera:i,setCameraActive:_,deregisterCamera:d,loop:Ue()};f.provide("useTres",p),p.scene.value.__tres={root:p},p.loop.register(()=>{a.value&&u.frames.value>0&&(b.value.render(e,a.value),o("render",p.renderer.value)),u.priority.value=0,u.mode.value==="always"?u.frames.value=1:u.frames.value=Math.max(0,u.frames.value-1)},"render");const{on:y,cancel:A}=ze(p);p.loop.setReady(!1),p.loop.start(),y(()=>{o("ready",p),p.loop.setReady(!0),Ne(e,p,o)}),f.onUnmounted(()=>{A(),p.loop.stop()});const L=100,S=M.useFps({every:L}),{isSupported:O,memory:T}=M.useMemory({interval:L}),k=160;let x=performance.now();const h=({timestamp:I})=>{p.scene.value&&(p.perf.memory.allocatedMem=he(p.scene.value)),I-x>=L&&(x=I,p.perf.fps.accumulator.push(S.value),p.perf.fps.accumulator.length>k&&p.perf.fps.accumulator.shift(),p.perf.fps.value=S.value,O.value&&T.value&&(p.perf.memory.accumulator.push(T.value.usedJSHeapSize/1024/1024),p.perf.memory.accumulator.length>k&&p.perf.memory.accumulator.shift(),p.perf.memory.currentMem=p.perf.memory.accumulator.reduce((U,$)=>U+$,0)/p.perf.memory.accumulator.length))};let P=0;const j=1,{pause:B}=M.useRafFn(({delta:I})=>{window.__TRES__DEVTOOLS__&&(h({timestamp:performance.now()}),P+=I,P>=j&&(window.__TRES__DEVTOOLS__.cb(p),P=0))},{immediate:!0});return f.onUnmounted(()=>{B()}),p}function Q(){const e=f.inject("useTres");if(!e)throw new Error("useTresContext must be used together with useTresContextProvider");return e}const en=Q;function tn(){const{camera:e,scene:t,renderer:n,loop:r,raycaster:o,controls:l,invalidate:s,advance:a}=Q();r.setContext({camera:e,scene:t,renderer:n,raycaster:o,controls:l,invalidate:s,advance:a});function c(_,u=0){return r.register(_,"before",u)}function i(_){return r.register(_,"render")}function d(_,u=0){return r.register(_,"after",u)}return{pause:r.pause,resume:r.resume,pauseRender:r.pauseRender,resumeRender:r.resumeRender,isActive:r.isActive,onBeforeRender:c,render:i,onAfterRender:d}}const qe=M.createEventHook(),Ye=M.createEventHook(),ve=M.createEventHook(),X=new v.Clock;let oe=0,se=0;const{pause:nn,resume:Ke,isActive:rn}=M.useRafFn(()=>{qe.trigger({delta:oe,elapsed:se,clock:X}),Ye.trigger({delta:oe,elapsed:se,clock:X}),ve.trigger({delta:oe,elapsed:se,clock:X})},{immediate:!1});ve.on(()=>{oe=X.getDelta(),se=X.getElapsedTime()});let Je=!1;const on=()=>(Je||(Je=!0,Ke()),{onBeforeLoop:qe.on,onLoop:Ye.on,onAfterLoop:ve.on,pause:nn,resume:Ke,isActive:rn});function sn(){const{logWarning:e}=z();function t(l,s,a){let c=null;return l.traverse(i=>{i[s]===a&&(c=i)}),c||e(`Child with ${s} '${a}' not found.`),c}function n(l,s,a){const c=[];return l.traverse(i=>{i[s].includes(a)&&c.push(i)}),c.length||e(`Children with ${s} '${a}' not found.`),c}function r(l,s){return t(l,"name",s)}function o(l,s){return n(l,"name",s)}return{seek:t,seekByName:r,seekAll:n,seekAllByName:o}}function an(e,t={},n={}){let r=e;const o=a=>{r=a};let l=new Proxy({},{});const s={has(a,c){return c in t||c in r},get(a,c,i){return c in t?t[c](r):r[c]},set(a,c,i){return n[c]?n[c](i,r,l,o):r[c]=i,!0}};return l=new Proxy({},s),l}const{logError:Qe}=z(),Xe=["onClick","onContextMenu","onPointerMove","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut","onDoubleClick","onPointerDown","onPointerUp","onPointerCancel","onPointerMissed","onLostPointerCapture","onWheel"],ln=e=>{const t=e.scene.value;function n(i,d,_,u){if(u||(u={}),u.args||(u.args=[]),i==="template"||kt(i))return null;let C=i.replace("Tres",""),m;if(i==="primitive"){(!R(u.object)||f.isRef(u.object))&&Qe("Tres primitives need an 'object' prop, whose value is an object or shallowRef<object>"),C=u.object.type;const b={};m=an(u.object,{object:y=>y,isPrimitive:()=>!0,__tres:()=>b},{object:(y,A,L,S)=>{Ft(y,L,S,{patchProp:l,remove:o,insert:r},e)},__tres:y=>{Object.assign(b,y)}})}else{const b=te.value[C];b||Qe(`${C} is not defined on the THREE namespace. Use extend to add it to the catalog.`),m=new b(...u.args)}return m?(m.isCamera&&(u!=null&&u.position||m.position.set(3,3,3),u!=null&&u.lookAt||m.lookAt(0,0,0)),m=N(m,{...m.__tres,type:C,memoizedProps:u,eventCount:0,primitive:i==="primitive",attach:u.attach},e),m):null}function r(i,d){var C,m,b;if(!i)return;d=d||t;const _=i.__tres?i:N(i,{},e),u=d.__tres?d:N(d,{},e);i=q(_),d=q(u),i.__tres&&((C=i.__tres)==null?void 0:C.eventCount)>0&&((m=e.eventManager)==null||m.registerObject(i)),e.registerCamera(i),(b=e.eventManager)==null||b.registerPointerMissedObject(i),_.__tres.attach?Et(u,_,_.__tres.attach):V(i)&&V(u)&&(u.add(i),i.dispatchEvent({type:"added"})),_.__tres.parent=u,u.__tres.objects&&!u.__tres.objects.includes(_)&&u.__tres.objects.push(_)}function o(i,d){var m,b,p,y;if(!i)return;i!=null&&i.__tres&&((m=i.__tres)==null?void 0:m.eventCount)>0&&((b=e.eventManager)==null||b.deregisterObject(i)),d=le(d)?"default":d;const _=(p=i.__tres)==null?void 0:p.dispose;le(_)||(_===null?d=!1:d=_);const u=(y=i.__tres)==null?void 0:y.primitive,C=d==="default"?!u:!!d;if(i.__tres&&"objects"in i.__tres&&[...i.__tres.objects].forEach(A=>o(A,d)),C&&i.children&&[...i.children].forEach(A=>o(A,d)),Ie(i,e),$e(i,e),C&&!Pt(i)){if(G(d))d(i);else if(G(i.dispose))try{i.dispose()}catch{}}"__tres"in i&&delete i.__tres}function l(i,d,_,u){var A,L;if(!i)return;let C=i,m=d;if(i.__tres&&(i.__tres.memoizedProps[d]=u),d==="attach"){const S=((A=i.__tres)==null?void 0:A.parent)||i.parent;o(i),N(i,{attach:u},e),S&&r(i,S);return}if(d==="dispose"){i.__tres||(i=N(i,{},e)),i.__tres.dispose=u;return}if(V(i)&&m==="blocks-pointer-events"){u||u===""?i[m]=u:delete i[m];return}Xe.includes(d)&&i.__tres&&(i.__tres.eventCount+=1);let b=fe(m),p=C==null?void 0:C[b];if(m==="args"){const S=i,O=_??[],T=u??[],k=((L=i.__tres)==null?void 0:L.type)||i.type;k&&O.length&&!Tt(O,T)&&(C=Object.assign(S,new te.value[k](...u)));return}if(C.type==="BufferGeometry"){if(m==="args")return;C.setAttribute(fe(m),new v.BufferAttribute(...u));return}if(m.includes("-")&&p===void 0){p=C;for(const S of m.split("-"))b=m=fe(S),C=p,p=p==null?void 0:p[m]}let y=u;if(y===""&&(y=!0),G(p)){Xe.includes(d)||(ce(y)?i[b](...y):i[b](y)),b.startsWith("on")&&G(y)&&(C[b]=y);return}Se(p)&&Se(y)?p.mask=y.mask:Pe(p)&&vt(y)?p.set(y):yt(p)&&_t(y)&&p.constructor===y.constructor?p.copy(y):ue(p)&&Array.isArray(y)?"fromArray"in p&&typeof p.fromArray=="function"?p.fromArray(y):p.set(...y):ue(p)&&typeof y=="number"?"setScalar"in p&&typeof p.setScalar=="function"?p.setScalar(y):p.set(y):C[b]=y,Be(i)}function s(i){var d;return((d=i==null?void 0:i.__tres)==null?void 0:d.parent)||null}function a(i){const d=N(new v.Object3D,{type:"Comment"},e);return d.name=i,d}function c(i){var C;const d=s(i),_=((C=d==null?void 0:d.__tres)==null?void 0:C.objects)||[],u=_.indexOf(i);return u<0||u>=_.length-1?null:_[u+1]}return{insert:r,remove:o,createElement:n,patchProp:l,parentNode:s,createText:()=>void 0,createComment:a,setText:()=>void 0,setElementText:()=>void 0,nextSibling:c,querySelector:()=>void 0,setScopeId:()=>void 0,cloneNode:()=>void 0,insertStaticContent:()=>void 0}};function cn(){return He().__VUE_DEVTOOLS_GLOBAL_HOOK__}function He(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const un=typeof Proxy=="function",fn="devtools-plugin:setup",dn="plugin:settings:set";let Y,ye;function pn(){var e;return Y!==void 0||(typeof window<"u"&&window.performance?(Y=!0,ye=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Y=!0,ye=globalThis.perf_hooks.performance):Y=!1),Y}function mn(){return pn()?ye.now():Date.now()}class hn{constructor(t,n){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=n;const r={};if(t.settings)for(const s in t.settings){const a=t.settings[s];r[s]=a.defaultValue}const o=`__vue-devtools-plugin-settings__${t.id}`;let l=Object.assign({},r);try{const s=localStorage.getItem(o),a=JSON.parse(s);Object.assign(l,a)}catch{}this.fallbacks={getSettings(){return l},setSettings(s){try{localStorage.setItem(o,JSON.stringify(s))}catch{}l=s},now(){return mn()}},n&&n.on(dn,(s,a)=>{s===this.plugin.id&&this.fallbacks.setSettings(a)}),this.proxiedOn=new Proxy({},{get:(s,a)=>this.target?this.target.on[a]:(...c)=>{this.onQueue.push({method:a,args:c})}}),this.proxiedTarget=new Proxy({},{get:(s,a)=>this.target?this.target[a]:a==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(a)?(...c)=>(this.targetQueue.push({method:a,args:c,resolve:()=>{}}),this.fallbacks[a](...c)):(...c)=>new Promise(i=>{this.targetQueue.push({method:a,args:c,resolve:i})})})}async setRealTarget(t){this.target=t;for(const n of this.onQueue)this.target.on[n.method](...n.args);for(const n of this.targetQueue)n.resolve(await this.target[n.method](...n.args))}}function gn(e,t){const n=e,r=He(),o=cn(),l=un&&n.enableEarlyProxy;if(o&&(r.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!l))o.emit(fn,e,t);else{const s=l?new hn(n,o):null;(r.__VUE_DEVTOOLS_PLUGINS__=r.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:s}),s&&t(s.proxiedTarget)}}function vn(e,t){const n=`▲ ■ ●${e}`;typeof Ze=="function"?Ze(n,t):console.log(n)}function Ze(e,t){throw new Error(e+t)}const et=e=>{const t={id:e.uuid,label:e.type,children:[],tags:[]};e.name!==""&&t.tags.push({label:e.name,textColor:5750629,backgroundColor:15793395});const n=he(e);return n>0&&t.tags.push({label:`${Kt(n)} KB`,textColor:15707189,backgroundColor:16775644,tooltip:"Memory usage"}),e.type.includes("Light")&&(Ct(e)&&t.tags.push({label:`${e.intensity}`,textColor:9738662,backgroundColor:16316922,tooltip:"Intensity"}),t.tags.push({label:`#${new v.Color(e.color).getHexString()}`,textColor:9738662,backgroundColor:16316922,tooltip:"Color"})),e.type.includes("Camera")&&(t.tags.push({label:`${e.fov}°`,textColor:9738662,backgroundColor:16316922,tooltip:"Field of view"}),t.tags.push({label:`x: ${Math.round(e.position.x)} y: ${Math.round(e.position.y)} z: ${Math.round(e.position.z)}`,textColor:9738662,backgroundColor:16316922,tooltip:"Position"})),t};function tt(e,t,n=""){e.children.forEach(r=>{if(r.type==="HightlightMesh"||n&&!r.type.includes(n)&&!r.name.includes(n))return;const o=et(r);t.children.push(o),tt(r,o,n)})}const yn=[],H="tres:inspector",_n=f.reactive({sceneGraph:null});function wn(e,t){gn({id:"dev.esm.tres",label:"TresJS 🪐",logo:"https://raw.githubusercontent.com/Tresjs/tres/main/public/favicon.svg",packageName:"tresjs",homepage:"https://tresjs.org",componentStateTypes:yn,app:e},n=>{typeof n.now!="function"&&vn("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.addInspector({id:H,label:"TresJS 🪐",icon:"account_tree",treeFilterPlaceholder:"Search instances"}),setInterval(()=>{n.sendInspectorTree(H)},1e3),setInterval(()=>{n.notifyComponentUpdate()},5e3),n.on.getInspectorTree(l=>{if(l.inspectorId===H){const s=et(t.scene.value);tt(t.scene.value,s,l.filter),_n.sceneGraph=s,l.rootNodes=[s]}});let r=null,o=null;n.on.getInspectorState(l=>{var s;if(l.inspectorId===H){const[a]=t.scene.value.getObjectsByProperty("uuid",l.nodeId);if(!a)return;if(o&&r&&r.parent&&o.remove(r),a.isMesh){const c=Bt(a);a.add(c),r=c,o=a}l.state={object:Object.entries(a).map(([c,i])=>c==="children"?{key:c,value:i.filter(d=>d.type!=="HightlightMesh")}:{key:c,value:i,editable:!0}).filter(({key:c})=>c!=="parent")},a.isScene&&(l.state={...l.state,state:[{key:"Scene Info",value:{objects:a.children.length,memory:he(a),calls:t.renderer.value.info.render.calls,triangles:t.renderer.value.info.render.triangles,points:t.renderer.value.info.render.points,lines:t.renderer.value.info.render.lines}},{key:"Programs",value:((s=t.renderer.value.info.programs)==null?void 0:s.map(c=>({...c,programName:c.name})))||[]}]})}}),n.on.editInspectorState(l=>{l.inspectorId===H&&jt(t.scene.value,l.nodeId,l.path,l.state.value)})})}const bn=["data-scene","data-tres"],nt=f.defineComponent({__name:"TresCanvas",props:{shadows:{type:Boolean,default:void 0},clearColor:{},toneMapping:{},shadowMapType:{},useLegacyLights:{type:Boolean,default:void 0},outputColorSpace:{},toneMappingExposure:{},renderMode:{default:"always"},dpr:{},camera:{},preset:{},windowSize:{type:Boolean,default:void 0},enableProvideBridge:{type:Boolean,default:!0},context:{},alpha:{type:Boolean,default:void 0},premultipliedAlpha:{type:Boolean},antialias:{type:Boolean,default:void 0},stencil:{type:Boolean,default:void 0},preserveDrawingBuffer:{type:Boolean,default:void 0},powerPreference:{},depth:{type:Boolean,default:void 0},failIfMajorPerformanceCaveat:{type:Boolean,default:void 0},precision:{},logarithmicDepthBuffer:{type:Boolean,default:void 0},reverseDepthBuffer:{type:Boolean}},emits:["render","click","double-click","context-menu","pointer-move","pointer-up","pointer-down","pointer-enter","pointer-leave","pointer-over","pointer-out","pointer-missed","wheel","ready"],setup(e,{expose:t,emit:n}){const r=e,o=n,l=f.useSlots(),s=f.ref(),a=f.shallowRef(new v.Scene),c=f.getCurrentInstance();ne(we);const i=(m,b=!1)=>f.defineComponent({setup(){var L;const p=(L=f.getCurrentInstance())==null?void 0:L.appContext;p&&(p.app=c==null?void 0:c.appContext.app);const y={};function A(S){S&&(S.parent&&A(S.parent),S.provides&&Object.assign(y,S.provides))}return c!=null&&c.parent&&r.enableProvideBridge&&(A(c.parent),Reflect.ownKeys(y).forEach(S=>{f.provide(S,y[S])})),f.provide("useTres",m),f.provide("extend",ne),typeof window<"u"&&wn(p==null?void 0:p.app,m),()=>f.h(f.Fragment,null,b?[]:l.default())}}),d=(m,b=!1)=>{const p=i(m,b),{render:y}=f.createRenderer(ln(m));y(f.h(p),a.value)},_=(m,b=!1)=>{de(m.scene.value),b&&(m.renderer.value.dispose(),m.renderer.value.renderLists.dispose(),m.renderer.value.forceContextLoss()),a.value.__tres={root:m}},u=f.shallowRef(null);t({context:u,dispose:()=>_(u.value,!0)});const C=()=>{_(u.value),d(u.value,!0)};return f.onMounted(()=>{const m=s;u.value=Ge({scene:a.value,canvas:m,windowSize:r.windowSize??!1,rendererOptions:r,emit:o});const{registerCamera:b,camera:p,cameras:y,deregisterCamera:A}=u.value;d(u.value);const L=()=>{const S=new v.PerspectiveCamera(45,window.innerWidth/window.innerHeight,.1,1e3);S.position.set(3,3,3),S.lookAt(0,0,0),b(S);const O=f.watchEffect(()=>{y.value.length>=2&&(S.removeFromParent(),A(S),O==null||O())})};f.watch(()=>r.camera,(S,O)=>{S&&b(S),O&&(O.removeFromParent(),A(O))},{immediate:!0}),p.value||L()}),f.onUnmounted(C),(m,b)=>(f.openBlock(),f.createElementBlock("canvas",{ref_key:"canvas",ref:s,"data-scene":a.value.uuid,class:f.normalizeClass(m.$attrs.class),"data-tres":`tresjs ${f.unref(pt).version}`,style:f.normalizeStyle({display:"block",width:"100%",height:"100%",position:m.windowSize?"fixed":"relative",top:0,left:0,pointerEvents:"auto",touchAction:"none",...m.$attrs.style})},null,14,bn))}}),Cn=["TresCanvas","TresLeches","TresScene"],Mn={template:{compilerOptions:{isCustomElement:e=>e.startsWith("Tres")&&!Cn.includes(e)||e==="primitive"}}},{logWarning:Pn}=z();let E=null;const Sn={updated:(e,t)=>{var o;const n=It(t);if(!n){Pn(`v-distance-to: problem with binding value: ${t.value}`);return}E&&(E.dispose(),e.parent.remove(E));const r=n.clone().sub(e.position);r.normalize(),E=new v.ArrowHelper(r,e.position,e.position.distanceTo(n),16776960),e.parent.add(E),console.table([["Distance:",e.position.distanceTo(n)],[`origin: ${e.name||e.type}`,`x:${e.position.x}, y:${e.position.y}, z:${(o=e.position)==null?void 0:o.z}`],[`Destiny: ${e.name||e.type}`,`x:${n.x}, y:${n.y}, z:${n==null?void 0:n.z}`]])},unmounted:e=>{E==null||E.dispose(),e.parent&&e.parent.remove(E)}};class rt extends v.Line{constructor(t,n){const r=[1,1,0,-1,1,0,-1,-1,0,1,-1,0,1,1,0],o=new v.BufferGeometry;o.setAttribute("position",new v.Float32BufferAttribute(r,3)),o.computeBoundingSphere();const l=new v.LineBasicMaterial({fog:!1});super(o,l),this.light=t,this.color=n,this.type="RectAreaLightHelper";const s=[1,1,0,-1,1,0,-1,-1,0,1,1,0,-1,-1,0,1,-1,0],a=new v.BufferGeometry;a.setAttribute("position",new v.Float32BufferAttribute(s,3)),a.computeBoundingSphere(),this.add(new v.Mesh(a,new v.MeshBasicMaterial({side:v.BackSide,fog:!1})))}updateMatrixWorld(){if(this.scale.set(.5*this.light.width,.5*this.light.height,1),this.color!==void 0)this.material.color.set(this.color),this.children[0].material.color.set(this.color);else{this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity);const t=this.material.color,n=Math.max(t.r,t.g,t.b);n>1&&t.multiplyScalar(1/n),this.children[0].material.color.copy(this.material.color)}this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld),this.children[0].matrixWorld.copy(this.matrixWorld)}dispose(){this.geometry.dispose(),this.material.dispose(),this.children[0].geometry.dispose(),this.children[0].material.dispose()}}const{logWarning:ot}=z();let ie,F;const kn={DirectionalLight:v.DirectionalLightHelper,PointLight:v.PointLightHelper,SpotLight:v.SpotLightHelper,HemisphereLight:v.HemisphereLightHelper,RectAreaLight:rt},An={mounted:e=>{if(!e.isLight){ot(`${e.type} is not a light`);return}ie=kn[e.type],e.parent.add(new ie(e,1,e.color.getHex()))},updated:e=>{F=e.parent.children.find(t=>t instanceof ie),!(F instanceof rt)&&F.update()},unmounted:e=>{if(!e.isLight){ot(`${e.type} is not a light`);return}F=e.parent.children.find(t=>t instanceof ie),F&&F.dispose&&F.dispose(),e.parent&&e.parent.remove(F)}},Ln={mounted:(e,t)=>{if(t.arg){console.log(`v-log:${t.arg}`,e[t.arg]);return}console.log("v-log",e)}},On={install(e){e.component("TresCanvas",nt)}};w.TresCanvas=nt,w.UseLoader=mt,w.UseTexture=Wt,w.catalogue=te,w.createRenderLoop=Ue,w.default=On,w.dispose=de,w.extend=ne,w.isProd=zt,w.normalizeColor=Ve,w.normalizeVectorFlexibleParam=Qt,w.onTresReady=Zt,w.templateCompilerOptions=Mn,w.traverseObjects=be,w.useCamera=Ee,w.useLoader=Ce,w.useLogger=z,w.useLoop=tn,w.useRaycaster=We,w.useRenderLoop=on,w.useRenderer=Fe,w.useSeek=sn,w.useTexture=Re,w.useTres=en,w.useTresContext=Q,w.useTresContextProvider=Ge,w.useTresEventManager=Ne,w.vDistanceTo=Sn,w.vLightHelper=An,w.vLog=Ln,Object.defineProperties(w,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
