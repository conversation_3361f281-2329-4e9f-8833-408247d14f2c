{"name": "@tresjs/core", "type": "module", "version": "4.3.6", "packageManager": "pnpm@10.6.3", "description": "Declarative ThreeJS using Vue Components", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Tresjs/tres.git"}, "keywords": ["vue", "3d", "threejs", "three", "threejs-vue"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/tres.js", "require": "./dist/tres.umd.cjs"}, "./components": {"types": "./dist/src/components/index.d.ts"}, "./composables": {"types": "./dist/src/composables/index.d.ts"}, "./types": {"types": "./dist/src/types/index.d.ts"}, "./utils": {"types": "./dist/src/utils/index.d.ts"}, "./*": "./*"}, "main": "./dist/tres.js", "module": "./dist/tres.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "publishConfig": {"access": "public"}, "scripts": {"dev": "pnpm --filter='./playground/vue' dev", "dev:nuxt": "pnpm --filter='./playground/nuxt' dev", "build": "vite build", "test": "vitest", "test:ci": "vitest run", "test:ui": "vitest --ui --coverage.enabled=true", "release": "release-it", "coverage": "vitest run --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "vitepress serve docs", "docs:preview": "vitepress preview docs", "docs:contributors": "esno scripts/update-contributors.ts"}, "peerDependencies": {"three": ">=0.133", "vue": ">=3.4"}, "dependencies": {"@alvarosabu/utils": "^3.2.0", "@vue/devtools-api": "^6.6.3", "@vueuse/core": "^12.5.0"}, "devDependencies": {"@release-it/conventional-changelog": "^10.0.0", "@stackblitz/sdk": "^1.11.0", "@tresjs/cientos": "4.1.0", "@tresjs/eslint-config": "^1.4.0", "@types/three": "^0.173.0", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.5", "@vue/test-utils": "^2.4.6", "eslint": "^9.19.0", "eslint-plugin-vue": "^9.32.0", "esno": "^4.8.0", "gsap": "^3.12.7", "jsdom": "^26.0.0", "kolorist": "^1.8.0", "ohmyfetch": "^0.4.21", "pathe": "^2.0.2", "release-it": "^18.1.2", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.14.0", "sponsorkit": "^16.3.0", "three": "^0.173.0", "unocss": "^65.4.3", "unplugin": "^2.1.2", "unplugin-vue-components": "^28.0.0", "vite": "^6.1.0", "vite-plugin-banner": "^0.8.0", "vite-plugin-dts": "4.5.0", "vite-plugin-inspect": "^10.1.0", "vite-plugin-require-transform": "^1.0.21", "vite-svg-loader": "^5.1.0", "vitepress": "1.6.3", "vitest": "3.0.5", "vue": "3.5.13", "vue-demi": "^0.14.10"}}