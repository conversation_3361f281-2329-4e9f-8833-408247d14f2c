{"name": "@tresjs/cientos", "type": "module", "version": "4.3.1", "packageManager": "pnpm@9.15.0", "description": "Collection of useful helpers and fully functional, ready-made abstractions for Tres", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Tresjs/cientos.git"}, "keywords": ["vue", "3d", "threejs", "three", "threejs-vue", "composables"], "maintainers": ["<PERSON><PERSON> (https://github.com/alvarosabu/)", "<PERSON> (https://github.com/JaimeTor<PERSON>)", "<PERSON> (https://github.com/andretchen0)"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/trescientos.js"}, "./core": {"types": "./dist/core/index.d.ts"}, "./*": "./*"}, "main": "./dist/trescientos.js", "module": "./dist/trescientos.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "publishConfig": {"access": "public"}, "scripts": {"dev": "cd playground/vue && pnpm dev", "dev:nuxt": "cd playground/nuxt && pnpm dev", "build": "vite build", "release": "release-it", "lint": "eslint .", "lint:fix": "eslint . --fix", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "peerDependencies": {"@tresjs/core": ">=4.2.1", "three": ">=0.133", "vue": ">=3.3"}, "dependencies": {"@vueuse/core": "^12.0.0", "camera-controls": "^2.9.0", "stats-gl": "^2.0.1", "stats.js": "^0.17.0", "three-custom-shader-material": "^5.4.0", "three-stdlib": "^2.34.0"}, "devDependencies": {"@release-it/conventional-changelog": "^10.0.0", "@tresjs/core": "^4.3.5", "@tresjs/eslint-config": "^1.4.0", "@types/node": "^22.10.1", "@types/three": "^0.176.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.16.0", "eslint-plugin-vue": "^9.32.0", "gsap": "^3.12.5", "kolorist": "^1.8.0", "pathe": "^1.1.2", "release-it": "^18.1.1", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-visualizer": "^5.12.0", "three": "^0.173.0", "typescript": "^5.7.2", "unocss": "^0.65.1", "vite": "^6.0.2", "vite-plugin-banner": "^0.8.0", "vite-plugin-dts": "4.3.0", "vite-plugin-glsl": "^1.3.1", "vite-svg-loader": "^5.1.0", "vitepress": "1.5.0"}}