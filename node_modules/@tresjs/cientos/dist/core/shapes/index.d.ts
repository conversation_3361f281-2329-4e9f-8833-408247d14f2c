import { default as Box } from './Box.vue';
import { default as CatmullRomCurve3 } from './CatmullRomCurve3.vue';
import { default as Circle } from './Circle.vue';
import { default as Cone } from './Cone.vue';
import { default as <PERSON>linder } from './Cylinder.vue';
import { default as Dodecahedron } from './Dodecahedron.vue';
import { default as Icosahedron } from './Icosahedron.vue';
import { default as Line2 } from './Line2.vue';
import { default as Octahedron } from './Octahedron.vue';
import { default as Plane } from './Plane.vue';
import { default as Ring } from './Ring.vue';
import { default as RoundedBox } from './RoundedBox.vue';
import { default as Sphere } from './Sphere.vue';
import { default as Superformula } from './Superformula.vue';
import { default as Tetrahedron } from './Tetrahedron.vue';
import { default as Torus } from './Torus.vue';
import { default as TorusKnot } from './TorusKnot.vue';
import { default as Tube } from './Tube.vue';
export { Box, CatmullRomCurve3, Circle, Cone, Cylinder, Dodecahedron, Icosahedron, Line2, Octahedron, Plane, Ring, RoundedBox, Sphere, Superformula, Tetrahedron, Torus, TorusKnot, Tube, };
