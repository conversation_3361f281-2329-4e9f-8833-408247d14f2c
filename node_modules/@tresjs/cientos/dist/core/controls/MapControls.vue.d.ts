import { MapControls } from 'three-stdlib';
import { TresVector3 } from '@tresjs/core';
import { Camera } from 'three';
export interface MapControlsProps {
    /**
     * Whether to make this the default controls.
     *
     * @default false
     * @type {boolean}
     * @memberof MapControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/MapControls
     */
    makeDefault?: boolean;
    /**
     * The camera to control.
     *
     * @type {Camera}
     * @memberof MapControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/MapControls
     */
    camera?: Camera;
    /**
     * The dom element to listen to.
     *
     * @type {HTMLElement}
     * @memberof MapControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/MapControls
     */
    domElement?: HTMLElement;
    /**
     * The target to orbit around.
     *
     * @type {TresVector3}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.target
     */
    target?: TresVector3;
    /**
     * Whether to enable damping (inertia)
     *
     * @default false
     * @type {boolean}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.enableDamping
     */
    enableDamping?: boolean;
    /**
     * The damping inertia used if `.enableDamping` is set to true
     *
     * @default 0.05
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.dampingFactor
     */
    dampingFactor?: number;
    /**
     * Set to true to automatically rotate around the target.
     *
     * @default false
     * @type {boolean}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.autoRotate
     */
    autoRotate?: boolean;
    /**
     * How fast to rotate around the target if `.autoRotate` is true.
     *
     * @default 2
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.autoRotateSpeed
     */
    autoRotateSpeed?: number;
    /**
     * Whether to enable panning.
     *
     * @default true
     * @type {boolean}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.enablePan
     */
    enablePan?: boolean;
    /**
     * How fast to pan the camera when the keyboard is used. Default is 7.0 pixels per keypress.
     *
     * @default 7.0
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.keyPanSpeed
     */
    keyPanSpeed?: number;
    /**
     * This object contains references to the keycodes for controlling camera panning.
     * Default is the 4 arrow keys.
     *
     * @default `{ LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }`
     * @type Record<string, string>
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.keys
     */
    keys?: Record<string, string>;
    /**
     * How far you can orbit horizontally, upper limit.
     * If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ],
     * with ( max - min < 2 PI ). Default is Infinity.
     *
     * @default Infinity
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.maxAzimuthAngle
     */
    maxAzimuthAngle?: number;
    /**
     * How far you can orbit horizontally, lower limit.
     * If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ],
     * with ( max - min < 2 PI ).
     * Default is - Infinity.
     *
     * @default -Infinity
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.minAzimuthAngle
     */
    minAzimuthAngle?: number;
    /**
     * How far you can orbit vertically, upper limit.
     * Range is 0 to Math.PI radians, and default is Math.PI.
     *
     * @default Math.PI
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.maxPolarAngle
     */
    maxPolarAngle?: number;
    /**
     * How far you can orbit vertically, lower limit.
     * Range is 0 to Math.PI radians, and default is 0.
     *
     * @default 0
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.minPolarAngle
     */
    minPolarAngle?: number;
    /**
     * The minimum distance of the camera to the target.
     * Default is 0.
     *
     * @default 0
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.minDistance
     */
    minDistance?: number;
    /**
     * The maximum distance of the camera to the target.
     * Default is Infinity.
     *
     * @default Infinity
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.maxDistance
     */
    maxDistance?: number;
    /**
     * The minimum field of view angle, in radians.
     * Default is 0.
     *
     * @default 0
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.minZoom
     */
    minZoom?: number;
    /**
     * The maximum field of view angle, in radians.
     * ( OrthographicCamera only ).
     * Default is Infinity.
     *
     * @default Infinity
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/index.html?q=orbi#examples/en/controls/OrbitControls.maxZoom
     */
    maxZoom?: number;
    touches?: {
        ONE?: number | undefined;
        TWO?: number | undefined;
    };
    /**
     * Whether to enable zooming.
     *
     * @default true
     * @type {boolean}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.enableZoom
     */
    enableZoom?: boolean;
    /**
     * How fast to zoom in and out. Default is 1.
     *
     * @default 1
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.zoomSpeed
     */
    zoomSpeed?: number;
    /**
     * Whether to enable rotating.
     *
     * @default true
     * @type {boolean}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.enableRotate
     */
    enableRotate?: boolean;
    /**
     * How fast to rotate around the target. Default is 1.
     *
     * @default 1
     * @type {number}
     * @memberof OrbitControlsProps
     * @see https://threejs.org/docs/#examples/en/controls/OrbitControls.rotateSpeed
     */
    rotateSpeed?: number;
}
declare const _default: import('vue').DefineComponent<MapControlsProps, {
    instance: import('vue').ShallowRef<MapControls | null, MapControls | null>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    end: (...args: any[]) => void;
    start: (...args: any[]) => void;
    change: (...args: any[]) => void;
}, string, import('vue').PublicProps, Readonly<MapControlsProps> & Readonly<{
    onEnd?: ((...args: any[]) => any) | undefined;
    onStart?: ((...args: any[]) => any) | undefined;
    onChange?: ((...args: any[]) => any) | undefined;
}>, {
    maxDistance: number;
    makeDefault: boolean;
    minPolarAngle: number;
    maxPolarAngle: number;
    minAzimuthAngle: number;
    maxAzimuthAngle: number;
    minDistance: number;
    minZoom: number;
    maxZoom: number;
    enableDamping: boolean;
    dampingFactor: number;
    autoRotate: boolean;
    autoRotateSpeed: number;
    enablePan: boolean;
    keyPanSpeed: number;
    enableZoom: boolean;
    zoomSpeed: number;
    enableRotate: boolean;
    rotateSpeed: number;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
export default _default;
