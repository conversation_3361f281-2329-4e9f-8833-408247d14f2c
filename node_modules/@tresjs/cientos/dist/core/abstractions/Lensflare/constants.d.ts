import { LensflareElementProps, SeedProps } from '.';
export declare const TEXTURE_PATH = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/";
export declare const circle = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/circle.png";
export declare const circleBlur = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/circleBlur.png";
export declare const circleRainbow = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/circleRainbow.png";
export declare const line = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/line.png";
export declare const poly6 = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/poly6.png";
export declare const polyStroke6 = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/polyStroke6.png";
export declare const rays = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/rays.png";
export declare const ring = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/ring.png";
export declare const starThin6 = "https://raw.githubusercontent.com/Tresjs/assets/93976c7d63ac83d4a254a41a10b2362bc17e90c9/textures/lensflare/starThin6.png";
export declare const oversize: SeedProps;
export declare const bodyRequired0: SeedProps;
export declare const bodyRequired1: SeedProps;
export declare const bodyOptional: SeedProps;
export declare const darkPurple: number, darkBlue: number;
export declare const front: SeedProps;
export declare const back: SeedProps;
export declare const defaultSeedProps: SeedProps[];
export declare const defaultLensflareElementProps: LensflareElementProps;
