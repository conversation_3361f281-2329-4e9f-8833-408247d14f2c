import { FboOptions } from '.';
declare const _default: import('vue').DefineComponent<FboOptions, {
    instance: import('vue').Ref<import('three').WebGLRenderTarget<import('three').Texture> | null, import('three').WebGLRenderTarget<import('three').Texture> | null>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<FboOptions> & Readonly<{}>, {
    depth: boolean;
    settings: import('three').RenderTargetOptions;
    autoRender: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
export default _default;
