import { MeshStandardMaterial, Matrix4, Texture } from 'three';
export declare class MeshReflectionMaterial extends MeshStandardMaterial {
    private _tDepth;
    private _distortionMap;
    private _tSharp;
    private _tBlur;
    private _textureMatrix;
    private _mix;
    private _sharpMix;
    private _blurMixSmooth;
    private _blurMixRough;
    private _sharpDepthEdgeMin;
    private _sharpDepthEdgeMax;
    private _sharpDepthScale;
    private _sharpDepthBias;
    private _distortion;
    constructor(parameters?: {});
    onBeforeCompile(shader: any): void;
    get tSharp(): Texture | null;
    set tSharp(v: Texture | null);
    get tDepth(): Texture | null;
    set tDepth(v: Texture | null);
    get distortionMap(): Texture | null;
    set distortionMap(v: Texture | null);
    get tBlur(): Texture | null;
    set tBlur(v: Texture | null);
    get textureMatrix(): Matrix4 | null;
    set textureMatrix(v: Matrix4 | null);
    get sharpMix(): number;
    set sharpMix(v: number);
    get blurMixSmooth(): number;
    set blurMixSmooth(v: number);
    get blurMixRough(): number;
    set blurMixRough(v: number);
    get mix(): number;
    set mix(v: number);
    get sharpDepthScale(): number;
    set sharpDepthScale(v: number);
    get sharpDepthBias(): number;
    set sharpDepthBias(v: number);
    get sharpDepthEdgeMin(): number;
    set sharpDepthEdgeMin(v: number);
    get sharpDepthEdgeMax(): number;
    set sharpDepthEdgeMax(v: number);
    get distortion(): number;
    set distortion(v: number);
}
