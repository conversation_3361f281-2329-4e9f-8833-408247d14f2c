declare const _default: import('vue').DefineComponent<{
    speed?: number;
    factor?: number;
}, {
    instance: import('vue').ShallowRef<any, any>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<{
    speed?: number;
    factor?: number;
}> & Readonly<{}>, {
    speed: number;
    factor: number;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
export default _default;
