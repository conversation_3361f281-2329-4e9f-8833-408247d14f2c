import { Texture } from 'three';
declare const _default: import('vue').DefineComponent<{
    args?: any[];
    form?: "circle" | "ring" | "rect" | any;
    toneMapped?: boolean;
    map?: Texture;
    intensity?: number;
    color?: any;
}, {
    mesh: import('vue').Ref<any, any>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<{
    args?: any[];
    form?: "circle" | "ring" | "rect" | any;
    toneMapped?: boolean;
    map?: Texture;
    intensity?: number;
    color?: any;
}> & Readonly<{}>, {
    map: Texture;
    color: any;
    args: any[];
    toneMapped: boolean;
    intensity: number;
    form: "circle" | "ring" | "rect" | any;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
export default _default;
