export declare const Stats: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    showPanel: {
        type: NumberConstructor;
        default: number;
    };
}>, void, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    showPanel: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{}>, {
    showPanel: number;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
