{"name": "@alvarosabu/utils", "type": "module", "version": "3.2.0", "packageManager": "pnpm@8.12.0", "description": "Personal collection of common JavaScript / TypeScript utils", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)", "keywords": ["utils", "javascript", "typescript"], "exports": {".": {"import": "./dist/as-utils.mjs"}}, "main": "./dist/as-utils.mjs", "types": "./dist/as-utils.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "unbuild", "preview": "vite preview", "lint": "eslint --ext .ts,.tsx .", "test": "vitest", "test:ci": "vitest run", "test:ui": "vitest --ui", "release": "release-it"}, "devDependencies": {"@alvarosabu/eslint-config": "^0.4.0", "@release-it/conventional-changelog": "^8.0.1", "@vitest/ui": "^1.3.1", "eslint": "^8.57.0", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vite": "^5.1.5", "vitest": "^1.3.1"}}