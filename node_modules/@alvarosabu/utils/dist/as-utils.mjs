const isBrowser = typeof window !== "undefined";
const isDefined = (val) => typeof val !== "undefined";
const hasValue = (val) => val !== void 0 && val !== null;
const isBoolean = (val) => typeof val === "boolean";
const isFunction = (val) => typeof val === "function";
const isNumber = (val) => typeof val === "number";
const isString = (val) => typeof val === "string";
const isWindow = (val) => typeof window !== "undefined" && toString.call(val) === "[object Window]";
const isArray = (a) => !!a && a.constructor === Array;
const isObject = (a) => !!a && a.constructor === Object;
const isEvent = (e) => !!e && e.constructor === Event;
const isPromise = (e) => !!e && e.constructor.name === "Promise";
const isEmpty = (entry) => {
  if (isArray(entry)) {
    return entry.length <= 0;
  }
  if (isObject(entry)) {
    return Object.entries(entry).length <= 0;
  }
  return false;
};

const slugify = (text) => text.toString().toLowerCase().replace(/\s+/g, "-").replace(/[^\w-]+/g, "").replace(/--+/g, "-").replace(/^-+/, "").replace(/-+$/, "");
const listSnakeToCamel = (arr) => arr.map((el) => {
  if (isArray(el)) {
    return listSnakeToCamel(el);
  }
  if (isObject(el)) {
    return snakeToCamel(el);
  }
  return el;
});
const listCamelToSnake = (arr) => arr.map((el) => {
  if (isArray(el)) {
    return listCamelToSnake(el);
  }
  if (isObject(el)) {
    return camelToSnake(el);
  }
  return el;
});
const snakeToCamel = (myObj) => {
  const newObj = {};
  Object.keys(myObj).forEach((key) => {
    if (isArray(myObj[key])) {
      newObj[key.replace(/(_\w)/g, (m) => m[1].toUpperCase())] = listSnakeToCamel(myObj[key]);
    } else if (isObject(myObj[key])) {
      newObj[key.replace(/(_\w)/g, (m) => m[1].toUpperCase())] = snakeToCamel(
        myObj[key]
      );
    } else {
      newObj[key.replace(/(_\w)/g, (m) => m[1].toUpperCase())] = myObj[key];
    }
  });
  return newObj;
};
const camelToSnake = (myObj) => {
  const newObj = {};
  Object.keys(myObj).forEach((key) => {
    if (isArray(myObj[key])) {
      newObj[key.replace(/([A-Z])/g, ($1) => `_${$1.toLowerCase()}`)] = myObj[key] = listCamelToSnake(myObj[key]);
    } else if (isObject(myObj[key])) {
      newObj[key.replace(/([A-Z])/g, ($1) => `_${$1.toLowerCase()}`)] = myObj[key] = camelToSnake(myObj[key]);
    } else {
      newObj[key.replace(/([A-Z])/g, ($1) => `_${$1.toLowerCase()}`)] = myObj[key];
    }
  });
  return newObj;
};
const listKebabToCamel = (arr) => arr.map((el) => {
  if (isArray(el)) {
    return listKebabToCamel(el);
  }
  if (isObject(el)) {
    return kebabToCamel(el);
  }
  return el;
});
const listCamelToKebab = (arr) => arr.map((el) => {
  if (isArray(el)) {
    return listCamelToKebab(el);
  }
  if (isObject(el)) {
    return camelToKebab(el);
  }
  return el;
});
const kebabToCamel = (myObj) => {
  const newObj = {};
  Object.keys(myObj).forEach((key) => {
    if (isArray(myObj[key])) {
      newObj[key.replace(/(-\w)/g, (m) => m[1].toUpperCase())] = listKebabToCamel(myObj[key]);
    } else if (isObject(myObj[key])) {
      newObj[key.replace(/(-\w)/g, (m) => m[1].toUpperCase())] = kebabToCamel(
        myObj[key]
      );
    } else {
      newObj[key.replace(/(-\w)/g, (m) => m[1].toUpperCase())] = myObj[key];
    }
  });
  return newObj;
};
const camelToKebab = (myObj) => {
  const newObj = {};
  Object.keys(myObj).forEach((key) => {
    if (isArray(myObj[key])) {
      newObj[key.replace(/([A-Z])/g, ($1) => `-${$1.toLowerCase()}`)] = myObj[key] = listCamelToKebab(myObj[key]);
    } else if (isObject(myObj[key])) {
      newObj[key.replace(/([A-Z])/g, ($1) => `-${$1.toLowerCase()}`)] = myObj[key] = camelToKebab(myObj[key]);
    } else {
      newObj[key.replace(/([A-Z])/g, ($1) => `-${$1.toLowerCase()}`)] = myObj[key];
    }
  });
  return newObj;
};

function clamp(n, min, max) {
  return Math.min(max, Math.max(min, n));
}

function notNullish(v) {
  return v != null;
}
function noNull(v) {
  return v !== null;
}
function notUndefined(v) {
  return v !== void 0;
}
function isTruthy(v) {
  return Boolean(v);
}

export { camelToKebab, camelToSnake, clamp, hasValue, isArray, isBoolean, isBrowser, isDefined, isEmpty, isEvent, isFunction, isNumber, isObject, isPromise, isString, isTruthy, isWindow, kebabToCamel, listCamelToKebab, listCamelToSnake, listKebabToCamel, listSnakeToCamel, noNull, notNullish, notUndefined, slugify, snakeToCamel };
