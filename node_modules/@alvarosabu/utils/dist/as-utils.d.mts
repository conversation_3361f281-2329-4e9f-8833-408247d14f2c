declare const isBrowser: boolean;
declare const isDefined: <T = any>(val?: T | undefined) => val is T;
declare const hasValue: <T = any>(val?: T | undefined) => val is T;
declare const isBoolean: (val: any) => val is boolean;
declare const isFunction: <T extends () => {}>(val: any) => val is T;
declare const isNumber: (val: any) => val is number;
declare const isString: (val: unknown) => val is string;
declare const isWindow: (val: any) => val is Window;
declare const isArray: (a: any) => boolean;
declare const isObject: (a: any) => boolean;
declare const isEvent: (e: any) => boolean;
declare const isPromise: (e: any) => boolean;
/**
 * Check if value is empty
 * @param  {Object|Array} entry
 */
declare const isEmpty: (entry: (string | {
    [key: string]: any;
})[] | {
    [key: string]: any;
}) => boolean;

/**
 * Returns slugify version of a string
 * @param  {string} text
 * @returns string
 */
declare const slugify: (text: string) => string;
interface KeyObject {
    [x: string]: any;
}
declare const listSnakeToCamel: any;
declare const listCamelToSnake: any;
declare const snakeToCamel: (myObj: KeyObject) => KeyObject;
declare const camelToSnake: (myObj: KeyObject) => KeyObject;
declare const listKebabToCamel: any;
declare const listCamelToKebab: any;
declare const kebabToCamel: (myObj: KeyObject) => KeyObject;
declare const camelToKebab: (myObj: KeyObject) => KeyObject;

declare function clamp(n: number, min: number, max: number): number;

/**
 * Type guard to filter out null-ish values
 *
 * @category Guards
 * @example array.filter(notNullish)
 */
declare function notNullish<T>(v: T | null | undefined): v is NonNullable<T>;
/**
 * Type guard to filter out null values
 *
 * @category Guards
 * @example array.filter(noNull)
 */
declare function noNull<T>(v: T | null): v is Exclude<T, null>;
/**
 * Type guard to filter out null-ish values
 *
 * @category Guards
 * @example array.filter(notUndefined)
 */
declare function notUndefined<T>(v: T): v is Exclude<T, undefined>;
/**
 * Type guard to filter out falsy values
 *
 * @category Guards
 * @example array.filter(isTruthy)
 */
declare function isTruthy<T>(v: T): v is NonNullable<T>;

/**
 * Function
 */
type Fn<T = void> = () => T;

export { type Fn, type KeyObject, camelToKebab, camelToSnake, clamp, hasValue, isArray, isBoolean, isBrowser, isDefined, isEmpty, isEvent, isFunction, isNumber, isObject, isPromise, isString, isTruthy, isWindow, kebabToCamel, listCamelToKebab, listCamelToSnake, listKebabToCamel, listSnakeToCamel, noNull, notNullish, notUndefined, slugify, snakeToCamel };
