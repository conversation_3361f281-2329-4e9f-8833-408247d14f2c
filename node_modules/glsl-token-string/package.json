{"name": "glsl-token-string", "version": "1.0.1", "description": "Converts an array of GLSL tokens to a plain source string", "main": "index.js", "license": "MIT", "scripts": {"test": "node test | tap-spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "dependencies": {}, "devDependencies": {"glsl-tokenizer": "^2.0.0", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "repository": {"type": "git", "url": "git://github.com/stackgl/glsl-token-string.git"}, "keywords": ["ecosystem:stackgl"], "homepage": "https://github.com/stackgl/glsl-token-string", "bugs": {"url": "https://github.com/stackgl/glsl-token-string/issues"}}