---
import { getPortfolios } from '~/lib/strapi';
import PageLayout from '~/layouts/PageLayout.astro';
import { Image } from 'astro:assets';
import Headline from '~/components/ui/Headline.astro';
import type { StrapiDataItem, Portfolio } from '~/types';

// 1. Astro 的 getStaticPaths 函数，用于生成所有动态页面
export async function getStaticPaths() {
	const portfolios = await getPortfolios();
	return portfolios.map((portfolio: StrapiDataItem<Portfolio>) => ({
		params: { slug: portfolio.attributes.slug },
		props: { portfolio },
	}));
}

// 2. 从 getStaticPaths 接收单个作品的数据
const { portfolio } = Astro.props;
const { title, description, coverImage, gallery } = portfolio.attributes;

const metadata = {
	title: `${title} - 忆帧定制作品集`,
	description: description.substring(0, 150), // 截取前150个字符作为页面描述
};
---

<PageLayout {metadata}>
	<section class="py-12 md:py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6">
			<!-- 封面大图 -->
			<div class="mb-8 md:mb-12">
				<Image
					src={coverImage.data.attributes.url}
					alt={coverImage.data.attributes.alternativeText || title}
					width={1024}
					height={576}
					class="w-full h-auto rounded-2xl shadow-lg object-cover"
				/>
			</div>

			<!-- 标题和描述 -->
			<div class="text-center mb-12">
				<Headline subtitle="作品详情" title={title} />
				<p class="text-lg text-muted max-w-3xl mx-auto mt-4">{description}</p>
			</div>

			<!-- 作品集图库 -->
			{
				gallery && gallery.data && gallery.data.length > 0 && (
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
						{gallery.data.map((image, index) => (
							<div class="overflow-hidden rounded-xl shadow-md">
								<Image
									src={image.attributes.url}
									alt={image.attributes.alternativeText || `${title} - 作品集图片 ${index + 1}`}
									width={800}
									height={600}
									class="w-full h-auto object-cover"
								/>
							</div>
						))}
					</div>
				)
			}
		</div>
	</section>
</PageLayout>
