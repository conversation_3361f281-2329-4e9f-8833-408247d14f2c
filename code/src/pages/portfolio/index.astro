---
import { getPortfolios } from '~/lib/strapi';
import PageLayout from '~/layouts/PageLayout.astro';
import Headline from '~/components/ui/Headline.astro';
import PortfolioGrid from '~/components/portfolio/PortfolioGrid.astro';
import type { StrapiDataItem, Portfolio } from '~/types';

// 从 Strapi 获取所有作品
const portfolios = await getPortfolios();

const metadata = {
	title: '作品集 - 忆帧定制',
	description: '探索我们的精选作品集，从故事动画短片到宠物IP设计，每一个项目都承载着独特的情感与创意。',
};
---

<PageLayout metadata={metadata}>
	<section class="py-16 md:py-20">
		<div class="max-w-xl sm:mx-auto lg:max-w-2xl mb-12 px-4 text-center">
			<Headline
				subtitle="我们的作品"
				title="情感与创意的交织"
				text="每一个项目都是一次独特的叙事之旅。我们与客户紧密合作，将他们的故事和愿愿景转化为独一无二的视觉艺术品。"
			/>
		</div>
		<PortfolioGrid items={portfolios} />
	</section>
</PageLayout>
