---
import Layout from '~/layouts/PageLayout.astro';
import { Image } from 'astro:assets';
import catImage from '~/assets/images/cat.jpg';
import qrImage from '~/assets/images/wechat-qr.png';

const metadata = {
  title: '联系我们 - 忆帧定制',
};
---

<Layout metadata={metadata}>
  <section class="py-16 md:py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6">
      <div class="text-center mb-12">
        <p class="text-base text-primary font-bold tracking-wide uppercase mb-2">联系我们</p>
        <h1 class="text-4xl md:text-5xl font-bold leading-tighter tracking-tighter mb-4 font-heading">
          期待与你共同创作
        </h1>
        <h3 class="text-2xl font-bold tracking-tight dark:text-white mb-4">准备好讲述你的故事了吗？</h3>
      </div>

      <div class="grid md:grid-cols-3 gap-8 items-start">
        <!-- 左侧：联系信息（占2列） -->
        <div class="md:col-span-2 space-y-8">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span class="text-white text-sm">💬</span>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">微信沟通 (推荐)</h3>
              <p class="text-muted">
                添加我们的项目助理微信，即可开启一对一咨询。无论是初步的想法沟通，还是具体的细节讨论，我们都在这里为你解答。
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span class="text-white text-sm">📧</span>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">电子邮件</h3>
              <p class="text-muted">
                如果您倾向于使用邮件沟通，可以发送您的需求和故事到我们的官方邮箱：
                <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a>。
                我们会在24小时内回复。
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span class="text-white text-sm">📱</span>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">社交媒体</h3>
              <p class="text-muted">
                关注我们的 <a href="#" class="text-primary hover:underline">小红书</a> 和 
                <a href="#" class="text-primary hover:underline">TikTok</a> 账号，获取最新的作品和优惠信息。
              </p>
            </div>
          </div>
        </div>

        <!-- 右侧：紧凑的联系卡片（占1列） -->
        <div class="md:col-span-1">
          <div class="bg-gradient-to-br from-white/70 to-white/50 dark:from-slate-800/70 dark:to-slate-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 sticky top-8">
            <!-- 小猫头像 -->
            <div class="text-center mb-4">
              <div class="relative inline-block">
                <Image 
                  src={catImage}
                  alt="可爱的小猫咪" 
                  class="w-16 h-16 rounded-full object-cover border-2 border-primary/40"
                  width={64}
                  height={64}
                />
                <!-- 在线状态指示器 -->
                <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <h4 class="text-sm font-bold text-primary mt-2 mb-1">萌萌助理</h4>
              <p class="text-xs text-muted">随时为您服务</p>
            </div>
            
            <!-- 分隔线 -->
            <div class="border-t border-gray-200/50 my-4"></div>
            
            <!-- 二维码 -->
            <div class="text-center">
              <div class="inline-block bg-white p-2 rounded-xl shadow-sm">
                <Image 
                  src={qrImage}
                  alt="联系我们的二维码" 
                  class="w-24 h-24 mx-auto"
                  width={96}
                  height={96}
                />
              </div>
              <p class="text-xs text-muted mt-2">扫码即刻联系</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>
