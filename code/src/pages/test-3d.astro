---
//3d测试时候再使用，需要修改文件类型
import Layout from '~/layouts/Layout.astro';
import Scene from '~/components/experimental/3d/Scene.vue';
---

<Layout
  metadata={{
    title: '3D Test Page',
  }}
>
  <!-- 
    The 3D scene has been archived. 
    To re-enable, uncomment the import statement above and the <Scene /> component below.
    You may also need to adjust the import path for the Scene component if it has been moved.
  -->
  <div class="absolute inset-0">
    <!-- Portal target for the 2D UI elements from Vue components -->
    <div id="ui-portal-target" class="absolute inset-0 z-10 pointer-events-none"></div>
    <Scene client:only="vue" />
  </div>
</Layout>
