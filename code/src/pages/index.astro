---
import Layout from '~/layouts/PageLayout.astro';
import { getPermalink } from '~/utils/permalinks';
import { getServices, getFeaturedPortfolios } from '~/lib/strapi'; // <-- 导入数据获取函数

import Hero from '~/components/widgets/Hero.astro';
import Note from '~/components/widgets/Note.astro';
import Features from '~/components/widgets/Features.astro';
import Features2 from '~/components/widgets/Features2.astro';
import Steps from '~/components/widgets/Steps.astro';
import Content from '~/components/widgets/Content.astro';
import BlogLatestPosts from '~/components/widgets/BlogLatestPosts.astro';
import FAQs from '~/components/widgets/FAQs.astro';
import Stats from '~/components/widgets/Stats.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

// --- 从 Strapi 获取数据 ---
const services = await getServices();
const featuredPortfolios = await getFeaturedPortfolios();

const metadata = {
  title: '忆帧定制 - 你的故事，独一无二的艺术品',
  ignoreTitleTemplate: true,
};
---

<Layout metadata={metadata}>
  <!-- Hero Widget ******************* -->

  <Hero
    actions={[
      {
        variant: 'primary',
        text: '开始我的定制',
        href: getPermalink('/start-customization'),
      },
      { text: '浏览作品集', href: getPermalink('/portfolio') },
    ]}
    image={{ src: '~/assets/images/hero-image.png', alt: '忆帧定制艺术创作过程' }}
  >
    <Fragment slot="title">
      你的故事，<br /> <span class="text-accent dark:text-white">独一无二的艺术品</span>
    </Fragment>

    <Fragment slot="subtitle">
      <span class="hidden sm:inline">
        从珍贵回忆到无限想象，我们用温暖的笔触和独特的视角，为你量身打造专属的动画、漫画与数字艺术，让每一帧都成为可以触摸的情感投资。
      </span>
    </Fragment>
  </Hero>

  <!-- Features Widget *************** -->

  <Features
    id="features"
    tagline="我们的服务"
    title="四大核心产品线"
    subtitle="我们将你的故事，通过不同的艺术形式，打造成独一无二的数字珍品。"
    items={services
      .filter((service) => service && service.attributes) // <-- 添加安全过滤
      .map((service) => ({
        title: service.attributes.name,
        description: service.attributes.description, // <-- 修正：直接使用字符串
        icon: service.attributes.icon,
      }))}
  />

  <!-- Steps Widget ****************** -->

  <Steps
    title="仅需四步，开启您的定制之旅"
    items={[
      {
        title: '第一步: <span class="font-medium">提交需求</span>',
        description:
          '通过我们的引导式表单，告诉我们您的故事、想法和期望风格，上传相关照片或素材。',
        icon: 'tabler:file-text',
      },
      {
        title: '第二步: <span class="font-medium">沟通与确认</span>',
        description:
          '我们的艺术指导将与您联系，深入沟通细节，确认创作方向和最终报价。',
        icon: 'tabler:message-circle',
      },
      {
        title: '第三步: <span class="font-medium">创作与打磨</span>',
        description:
          '艺术家开始创作。过程中，我们会向您展示草图或半成品，并根据您的反馈进行调整，直至完美。',
        icon: 'tabler:paint',
      },
      {
        title: '第四步: <span class="font-medium">交付与分享</span>',
        description: '在您满意后，我们将交付最终的高清数字艺术品，并鼓励您与亲友分享这份喜悦。',
        icon: 'tabler:gift',
      },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1616198814651-e71f960c3180?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80',
      alt: '定制流程示意图',
    }}
  />

  <!-- Features2 Widget ************** -->

  <Features2
    title="精选作品展示"
    subtitle="每一个作品都承载着一个独特的故事和一份真挚的情感。这里是部分客户与我们共同创作的成果。"
    tagline="作品集"
    items={featuredPortfolios
      .filter((portfolio) => portfolio && portfolio.attributes) // <-- 添加安全过滤
      .map((portfolio) => ({
        title: portfolio.attributes.title,
        description: portfolio.attributes.description,
        // href: getPortfolioPermalink(portfolio.attributes.slug), // Temporarily disabled
        // 传递图片信息给组件
        image: {
          src: portfolio.attributes.coverImage.data.attributes.url,
          alt: portfolio.attributes.coverImage.data.attributes.alternativeText || portfolio.attributes.title,
        },
      }))}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-transparent"></div>
    </Fragment>
  </Features2>

  <!-- HighlightedPosts Widget ******* -->

  <BlogLatestPosts
    title="Find out more content in our Blog"
    information={`The blog is used to display AstroWind documentation.
                        Each new article will be an important step that you will need to know to be an expert in creating a website using Astro + Tailwind CSS.
                        Astro is a very interesting technology. Thanks.
                `}
  />

  <!-- FAQs Widget ******************* -->

  <FAQs
    title="Frequently Asked Questions"
    subtitle="Dive into the following questions to gain insights into the powerful features that AstroWind offers and how it can elevate your web development journey."
    tagline="FAQs"
    classes={{ container: 'max-w-6xl' }}
    items={[
      {
        title: 'Why AstroWind?',
        description:
          "Michael Knight a young loner on a crusade to champion the cause of the innocent. The helpless. The powerless in a world of criminals who operate above the law. Here he comes Here comes Speed Racer. He's a demon on wheels.",
      },
      {
        title: 'What do I need to start?',
        description:
          'Space, the final frontier. These are the voyages of the Starship Enterprise. Its five-year mission: to explore strange new worlds. Many say exploration is part of our destiny, but it’s actually our duty to future generations.',
      },
      {
        title: 'How to install the Astro + Tailwind CSS template?',
        description:
          "Well, the way they make shows is, they make one show. That show's called a pilot. Then they show that show to the people who make shows, and on the strength of that one show they decide if they're going to make more shows.",
      },
      {
        title: "What's something that you don't understand?",
        description:
          "A flower in my garden, a mystery in my panties. Heart attack never stopped old Big Bear. I didn't even know we were calling him Big Bear.",
      },
      {
        title: 'What is something that you would like to try again?',
        description:
          "A business big enough that it could be listed on the NASDAQ goes belly up. Disappears! It ceases to exist without me. No, you clearly don't know who you're talking to, so let me clue you in.",
      },
      {
        title: 'If you could only ask one question to each person you meet, what would that question be?',
        description:
          "This is not about revenge. This is about justice. A lot of things can change in twelve years, Admiral. Well, that's certainly good to know. About four years. I got tired of hearing how young I looked.",
      },
    ]}
  />

  <!-- Stats Widget ****************** -->

  <Stats
    stats={[
      { title: 'Downloads', amount: '132K' },
      { title: 'Stars', amount: '24.8K' },
      { title: 'Forks', amount: '10.3K' },
      { title: 'Users', amount: '48.4K' },
    ]}
  />

  <!-- CallToAction Widget *********** -->

  <CallToAction
    actions={[
      {
        variant: 'primary',
        text: 'Get template',
        href: 'https://github.com/onwidget/astrowind',
        target: '_blank',
        icon: 'tabler:download',
      },
    ]}
  >
    <Fragment slot="title">
      Astro&nbsp;+&nbsp;<br class="block sm:hidden" /><span class="sm:whitespace-nowrap">Tailwind CSS</span>
    </Fragment>

    <Fragment slot="subtitle">
      Be very surprised by these huge fake numbers you are seeing on this page. <br class="hidden md:inline" />Don't
      waste more time! :P
    </Fragment>
  </CallToAction>
</Layout>
