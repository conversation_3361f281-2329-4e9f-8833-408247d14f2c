---
import '~/assets/styles/tailwind.css';

import { I18N } from 'astrowind:config';

import CommonMeta from '~/components/common/CommonMeta.astro';
import Favicons from '~/components/Favicons.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import ApplyColorMode from '~/components/common/ApplyColorMode.astro';
import Metadata from '~/components/common/Metadata.astro';
import SiteVerification from '~/components/common/SiteVerification.astro';
import Analytics from '~/components/common/Analytics.astro';
import BasicScripts from '~/components/common/BasicScripts.astro';

// Comment the line below to disable View Transitions
import { ClientRouter } from 'astro:transitions';

import type { MetaData as MetaDataType } from '~/types';

export interface Props {
  metadata?: MetaDataType;
}

const { metadata = {} } = Astro.props;
const { language, textDirection } = I18N;
---

<!doctype html>
<html lang={language} dir={textDirection} class="2xl:text-[20px]">
  <head>
    <!-- Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet"/>

    <CommonMeta />
    <Favicons />
    <CustomStyles />
    <ApplyColorMode />
    <Metadata {...metadata} />
    <SiteVerification />
    <Analytics />

    <!-- Comment the line below to disable View Transitions -->
    <ClientRouter fallback="swap" />
  </head>

  <body class="antialiased text-default bg-page tracking-tight">
    <slot />

    <BasicScripts />
  </body>
</html>
