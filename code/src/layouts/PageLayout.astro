---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';
import Announcement from '~/components/widgets/Announcement.astro';

import { headerData, footerData } from '~/navigation';

import type { MetaData } from '~/types';

export interface Props {
  metadata?: MetaData;
}

const { metadata } = Astro.props;
---

<Layout metadata={metadata}>
  <slot name="announcement">
    <Announcement />
  </slot>
  <slot name="header">
    <Header {...headerData} isSticky {/* showRssFeed showToggleTheme */} />
  </slot>
  <main>
    <slot />
  </main>
  <slot name="footer">
    <Footer {...footerData} />
  </slot>
</Layout>
