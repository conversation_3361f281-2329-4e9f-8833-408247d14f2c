// src/lib/strapi.ts

// --- TypeScript 类型定义 ---

interface StrapiMediaFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  path: string | null;
  width: number;
  height: number;
  size: number;
  url: string;
}

interface StrapiMedia {
  id: number;
  attributes: {
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      thumbnail?: StrapiMediaFormat;
      small?: StrapiMediaFormat;
      medium?: StrapiMediaFormat;
      large?: StrapiMediaFormat;
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
  };
}

interface StrapiRichTextNode {
  type: 'paragraph' | 'heading' | 'list' | 'image' | 'quote';
  children: {
    type: 'text' | 'link';
    text?: string;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    strikethrough?: boolean;
    url?: string;
  }[];
}

interface StrapiDataItem<T> {
  id: number;
  attributes: T;
}

interface StrapiAPIResponse<T> {
  data: T;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// --- 作品 (Portfolio) 类型 ---

export interface Portfolio {
  title: string;
  description: string;
  category: 'animated_story' | 'digital_comic' | 'digital_portrait' | 'pet_ip_workshop';
  slug: string;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  coverImage: { data: StrapiMedia };
  gallery: { data: StrapiMedia[] };
}

// --- 服务 (Service) 类型 ---

export interface Service {
  name: string;
  description: string; // <-- 修正：从富文本数组改为字符串
  icon: string;
  order: number;
}


// --- 辅助函数：转换 Strapi 媒体 URL ---

/**
 * 将 Strapi 返回的相对 URL 转换为完整的 URL
 * @param relativeUrl - Strapi 返回的媒体 URL (例如 /uploads/image.jpg)
 * @returns 完整的 URL (例如 http://localhost:1337/uploads/image.jpg)
 */
function formatImageUrl(relativeUrl: string | undefined | null): string {
    if (!relativeUrl) {
      // 返回一个占位符图片或空字符串
      return 'https://via.placeholder.com/800x600.png?text=Image+Not+Found';
    }
    return `${STRAPI_URL}${relativeUrl}`;
}

// --- API 请求封装 ---

const STRAPI_URL = import.meta.env.STRAPI_URL;
const STRAPI_API_TOKEN = import.meta.env.STRAPI_API_TOKEN;

async function fetchData<T>(endpoint: string): Promise<T | null> {
  const url = `${STRAPI_URL}/api/${endpoint}`;

  if (!STRAPI_URL || !STRAPI_API_TOKEN) {
    console.error('CRITICAL: Strapi URL or API Token is not configured in .env file.');
    return null;
  }

  const headers = {
    'Authorization': `Bearer ${STRAPI_API_TOKEN}`,
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const responseData = await response.json().catch(() => ({}));
      console.error(`Strapi API Error: ${response.statusText} - ${JSON.stringify(responseData)}`);
      return null;
    }
    
    return await response.json();
  } catch (error: any) {
    // Gracefully handle network errors (e.g., backend is down)
    if (error.cause && error.cause.code === 'ECONNREFUSED') {
      console.warn(`[Strapi Connection Warn] Could not connect to Strapi service at ${url}. This is expected if the service is not running. Returning empty data.`);
    } else {
      // For other types of errors, log them as a full error for debugging
      console.error(`[Strapi Fetch Error] Failed to fetch from Strapi: ${url}`, error);
    }
    return null;
  }
}

// --- 数据获取函数 ---

/**
 * 获取所有服务列表，并按 `order` 字段升序排列
 * 手动将返回的数据包装在 attributes 对象中，以统一数据结构
 */
export async function getServices(): Promise<StrapiDataItem<Service>[]> {
  const response = await fetchData<StrapiAPIResponse<any[]>>('services?sort=order:asc');
  
  if (!response) {
    return [];
  }

  // 手动包装数据以匹配其他 API 的结构
  const wrappedData = response.data.map(item => ({
    id: item.id,
    attributes: {
      name: item.name,
      description: item.description,
      icon: item.icon,
      order: item.order,
    }
  }));

  return wrappedData;
}

/**
 * 获取所有被标记为“精选”的作品
 * (重写以适配扁平化的 API 响应，并转换为组件期望的嵌套结构)
 */
export async function getFeaturedPortfolios(): Promise<StrapiDataItem<Portfolio>[]> {
	// 注意: API 返回的是扁平结构, 所以我们用 any[] 来接收
	const response = await fetchData<StrapiAPIResponse<any[]>>('portfolios?populate=*&filters[isFeatured][$eq]=true');

	if (!response) {
    return [];
  }

	// 将扁平的 API 响应转换为前端组件期望的嵌套结构
	const transformedData = response.data.map((item) => {
		// --- 媒体字段转换 ---

		// 1. 处理封面图 (单个媒体字段)
		let coverImageForComponent;
		if (item.coverImage && item.coverImage.url) {
			// 如果图片存在, 格式化 URL 并包装成组件期望的结构
			coverImageForComponent = {
				data: {
					attributes: {
						...item.coverImage,
						url: formatImageUrl(item.coverImage.url),
					},
				},
			};
		} else {
			// 如果图片缺失, 提供一个备用的占位图结构
			coverImageForComponent = {
				data: {
					attributes: {
						url: formatImageUrl(null), // 获取占位图 URL
						alternativeText: '封面图丢失',
					},
				},
			};
		}

		// 2. 处理作品画廊 (多个媒体字段)
		let galleryForComponent;
		if (item.gallery && Array.isArray(item.gallery)) {
			galleryForComponent = {
				data: item.gallery.map((img) => ({
					attributes: {
						...img,
						url: formatImageUrl(img.url),
					},
				})),
			};
		} else {
			galleryForComponent = { data: [] };
		}

		// --- 组装最终的 Attributes 对象 ---
		const attributes = {
			...item, // 复制 title, description 等字段
			coverImage: coverImageForComponent,
			gallery: galleryForComponent,
		};

		// 返回符合 StrapiDataItem<Portfolio> 类型的最终对象
		return {
			id: item.id,
			attributes: attributes,
		};
	});

	return transformedData;
}

/**
 * 获取所有已发布的作品
 * (适配扁平化的 API 响应，并转换为组件期望的嵌套结构)
 */
export async function getPortfolios(): Promise<StrapiDataItem<Portfolio>[]> {
	// 注意: API 返回的是扁平结构, 所以我们用 any[] 来接收
	const response = await fetchData<StrapiAPIResponse<any[]>>('portfolios?populate=*&sort=createdAt:desc');

	if (!response) {
    return [];
  }

	// 将扁平的 API 响应转换为前端组件期望的嵌套结构
	const transformedData = response.data.map((item) => {
		// --- 媒体字段转换 ---

		// 1. 处理封面图 (单个媒体字段)
		let coverImageForComponent;
		if (item.coverImage && item.coverImage.url) {
			coverImageForComponent = {
				data: { attributes: { ...item.coverImage, url: formatImageUrl(item.coverImage.url) } },
			};
		} else {
			coverImageForComponent = {
				data: { attributes: { url: formatImageUrl(null), alternativeText: '封面图丢失' } },
			};
		}

		// 2. 处理作品画廊 (多个媒体字段)
		let galleryForComponent;
		if (item.gallery && Array.isArray(item.gallery)) {
			galleryForComponent = {
				data: item.gallery.map((img) => ({
					attributes: {
						...img,
						url: formatImageUrl(img.url),
					},
				})),
			};
		} else {
			galleryForComponent = { data: [] };
		}

		// --- 组装最终的 Attributes 对象 ---
		const attributes: Portfolio = {
			title: item.title,
			description: item.description,
			category: item.category,
			slug: item.slug,
			isFeatured: item.isFeatured,
			createdAt: item.createdAt,
			updatedAt: item.updatedAt,
			publishedAt: item.publishedAt,
			coverImage: coverImageForComponent,
			gallery: galleryForComponent,
		};

		// 返回符合 StrapiDataItem<Portfolio> 类型的最终对象
		return {
			id: item.id,
			attributes: attributes,
		};
	});

	return transformedData;
}

/**
 * (未来使用) 获取所有作品
 */
export async function getAllPortfolios(): Promise<StrapiDataItem<Portfolio>[]> {
  const response = await fetchData<StrapiAPIResponse<StrapiDataItem<Portfolio>[]>>('portfolios?populate=*');
  return response ? response.data : [];
}

/**
 * (未来使用) 根据 slug 获取单个作品
 */
export async function getPortfolioBySlug(slug: string): Promise<StrapiDataItem<Portfolio> | null> {
  const response = await fetchData<StrapiAPIResponse<StrapiDataItem<Portfolio>[]>>(`portfolios?populate=*&filters[slug][$eq]=${slug}`);
  if (!response || response.data.length === 0) {
    return null;
  }
  return response.data[0];
}
