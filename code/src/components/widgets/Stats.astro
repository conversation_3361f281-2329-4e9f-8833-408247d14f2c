---
import type { Stats as Props } from '~/types';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Headline from '~/components/ui/Headline.astro';
import { Icon } from 'astro-icon/components';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  stats = [],

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-6xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />
  <div class="flex flex-wrap justify-center -m-4 text-center">
    {
      stats &&
        stats.map(({ amount, title, icon }) => (
          <div class="p-4 md:w-1/4 sm:w-1/2 w-full min-w-[220px] text-center md:border-r md:last:border-none dark:md:border-slate-500 intersect-once motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade intersect-quarter">
            {icon && (
              <div class="flex items-center justify-center mx-auto mb-4 text-primary">
                <Icon name={icon} class="w-10 h-10" />
              </div>
            )}
            {amount && (
              <div class="font-heading text-primary text-[2.6rem] font-bold dark:text-white lg:text-5xl xl:text-6xl">
                {amount}
              </div>
            )}
            {title && (
              <div class="text-sm font-medium uppercase tracking-widest text-gray-800 dark:text-slate-400 lg:text-base">
                {title}
              </div>
            )}
          </div>
        ))
    }
  </div>
</WidgetWrapper>
