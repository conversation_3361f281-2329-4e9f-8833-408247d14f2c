---
import type { StrapiDataItem } from '~/types';
import type { Portfolio } from '~/types';
import { Image } from 'astro:assets';
import { getPortfolioPermalink } from '~/utils/permalinks';

export interface Props {
	items: StrapiDataItem<Portfolio>[];
}

const { items = [] } = Astro.props;
---

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
	{
		items.map((item) => (
			<div class="overflow-hidden rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:shadow-2xl hover:-translate-y-1 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border border-white/20">
				<a href={getPortfolioPermalink(item.attributes.slug)}>
					<div class="relative w-full aspect-[4/3] overflow-hidden">
						<Image
							src={item.attributes.coverImage.data.attributes.url}
							alt={item.attributes.coverImage.data.attributes.alternativeText || item.attributes.title}
							class="object-cover w-full h-full transition-transform duration-500 ease-in-out hover:scale-110"
							width={400}
							height={300}
							sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
						/>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold mb-2 font-heading">{item.attributes.title}</h3>
						<p class="text-muted text-sm line-clamp-3">{item.attributes.description}</p>
					</div>
				</a>
			</div>
		))
	}
</div>
