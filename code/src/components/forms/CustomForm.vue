<template>
  <div class="bg-white/50 dark:bg-slate-800/50 p-6 md:p-8 rounded-lg shadow-md backdrop-blur-sm border border-white/20">
    <!-- 步骤指示器 -->
    <div class="flex justify-between items-center mb-6">
      <div
        v-for="step in totalSteps"
        :key="step"
        class="w-full h-2 rounded-full mx-1 transition-colors duration-500"
        :class="step <= currentStep ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-700'"
      ></div>
    </div>

    <!-- 表单内容 -->
    <transition name="slide-fade" mode="out-in">
      <div :key="currentStep">
        <!-- 步骤一：选择服务 -->
        <div v-if="currentStep === 1">
          <h2 class="text-2xl font-bold mb-4 font-heading">第一步：选择您需要的服务</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div
              v-for="service in services"
              :key="service.id"
              @click="formData.service = service.id"
              class="p-4 rounded-lg border-2 cursor-pointer transition-all duration-300"
              :class="formData.service === service.id ? 'border-primary bg-primary/10' : 'border-gray-300 dark:border-gray-600 hover:border-primary/50'"
            >
              <h3 class="font-bold text-lg">{{ service.name }}</h3>
              <p class="text-sm text-muted">{{ service.description }}</p>
            </div>
          </div>
        </div>

        <!-- 步骤二：描述您的故事 -->
        <div v-if="currentStep === 2">
          <h2 class="text-2xl font-bold mb-4 font-heading">第二步：请告诉我们您的故事</h2>
          <textarea
            v-model="formData.story"
            rows="8"
            class="w-full p-3 rounded-lg border-2 bg-transparent border-gray-300 dark:border-gray-600 focus:border-primary outline-none transition-colors"
            placeholder="例如：这是我和我的猫“米米”的第一个周年纪念，它是一只橘猫，性格非常温顺，喜欢躺在窗边晒太阳。我希望能把它画成一个宇航员的形象，背景是星空..."
          ></textarea>
        </div>

        <!-- 步骤三：您的联系方式 -->
        <div v-if="currentStep === 3">
          <h2 class="text-2xl font-bold mb-4 font-heading">第三步：如何称呼您？</h2>
          <div class="space-y-4">
            <div>
              <label for="name" class="block text-sm font-medium mb-1">您的姓名或昵称</label>
              <input
                type="text"
                id="name"
                v-model="formData.name"
                class="w-full p-3 rounded-lg border-2 bg-transparent border-gray-300 dark:border-gray-600 focus:border-primary outline-none transition-colors"
                placeholder="例如：王女士"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium mb-1">您的电子邮箱</label>
              <input
                type="email"
                id="email"
                v-model="formData.email"
                class="w-full p-3 rounded-lg border-2 bg-transparent border-gray-300 dark:border-gray-600 focus:border-primary outline-none transition-colors"
                placeholder="我们会通过此邮箱与您联系"
              />
            </div>
          </div>
        </div>

        <!-- 步骤四：提交成功 -->
        <div v-if="currentStep === 4">
          <h2 class="text-2xl font-bold mb-4 font-heading">感谢您的提交！</h2>
          <p class="text-muted">
            我们的艺术指导将在 24 小时内通过您提供的联系方式与您沟通，请留意您的邮件或微信。
          </p>
        </div>
      </div>
    </transition>

    <!-- 导航按钮 -->
    <div class="flex justify-between mt-8" v-if="currentStep < 4">
      <button
        @click="prevStep"
        :disabled="currentStep === 1"
        class="px-6 py-2 rounded-lg font-bold transition-all duration-300"
        :class="{
          'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500': currentStep > 1,
          'bg-gray-300 dark:bg-gray-700 text-gray-500 cursor-not-allowed': currentStep === 1,
        }"
      >
        上一步
      </button>
      <button
        @click="nextStep"
        :disabled="!isStepValid"
        class="px-6 py-2 rounded-lg font-bold transition-all duration-300"
        :class="{
          'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500': isStepValid,
          'bg-gray-300 dark:bg-gray-700 text-gray-500 cursor-not-allowed': !isStepValid,
        }"
      >
        {{ currentStep === totalSteps ? '提交' : '下一步' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';

const currentStep = ref(1);
const totalSteps = 3; // 总共的业务步骤

const formData = reactive({
  service: null,
  story: '',
  name: '',
  email: '',
});

// 示例服务数据 (未来可以从 Strapi 获取)
const services = ref([
  { id: 'animated_story', name: '故事动画短片', description: '将您的珍贵回忆制作成 15-60 秒的动画短片。' },
  { id: 'digital_comic', name: '数字叙事漫画', description: '用定制化风格的漫画，讲述您的独特故事。' },
  { id: 'digital_portrait', name: '风格化数字肖像', description: '将您的照片转化为多种艺术风格的数字绘画。' },
  { id: 'pet_ip_workshop', name: '宠物IP工坊', description: '为您的爱宠打造专属 IP 形象、插画和表情包。' },
]);

// 验证当前步骤是否完成
const isStepValid = computed(() => {
  if (currentStep.value === 1) {
    return formData.service !== null;
  }
  if (currentStep.value === 2) {
    return formData.story.trim() !== '';
  }
  if (currentStep.value === 3) {
    // 简单的邮箱格式校验
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return formData.name.trim() !== '' && emailRegex.test(formData.email);
  }
  return true;
});

const nextStep = () => {
  if (isStepValid.value) {
    if (currentStep.value < totalSteps) {
      currentStep.value++;
    } else {
      // 调用新的提交函数
      handleSubmit();
    }
  }
};

const handleSubmit = async () => {
  // 从环境变量中获取 Strapi API 的 URL
  const strapiUrl = import.meta.env.PUBLIC_STRAPI_API_URL || 'http://localhost:1337';
  const endpoint = `${strapiUrl}/api/submissions`;

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      // Strapi v4 需要将数据包装在 "data" 字段中
      body: JSON.stringify({
        data: {
          service: formData.service,
          story: formData.story,
          name: formData.name,
          email: formData.email,
        }
      }),
    });

    if (!response.ok) {
      // 如果服务器返回错误，则抛出异常
      const errorData = await response.json();
      throw new Error(`API Error: ${errorData.error.message}`);
    }

    // 如果提交成功，跳转到感谢页面
    currentStep.value++;

  } catch (error) {
    console.error('表单提交失败:', error);
    // 在这里可以添加用户友好的错误提示，例如弹出一个提示框
    alert(`提交失败，请稍后重试。错误信息: ${error.message}`);
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};
</script>

<style>
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
