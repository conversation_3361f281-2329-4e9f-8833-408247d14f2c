---
import type { Item } from '~/types';
import { twMerge } from 'tailwind-merge';
import { Image } from 'astro:assets'; // <-- 导入 Image 组件
import { Icon } from 'astro-icon/components';
import Button from './Button.astro';

export interface Props {
  items?: Array<Item>;
  columns?: number;
  defaultIcon?: string;
  classes?: Record<string, string>;
}

const { items = [], columns, defaultIcon = '', classes = {} } = Astro.props;

const {
  container: containerClass = '',
  // container: containerClass = "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  panel: panelClass = '',
  title: titleClass = '',
  description: descriptionClass = '',
  icon: defaultIconClass = 'text-primary',
} = classes;
---

{
  items && items.length > 0 && (
    <div
      class={twMerge(
        `grid gap-8 gap-x-12 sm:gap-y-8 ${
          columns === 4
            ? 'lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2'
            : columns === 3
              ? 'lg:grid-cols-3 sm:grid-cols-2'
              : columns === 2
                ? 'sm:grid-cols-2 '
                : ''
        }`,
        containerClass
      )}
    >
      {items.map(({ title, description, icon, image, callToAction, classes: itemClasses = {} }) => ( // <-- 添加 image 属性
        <div class="relative flex flex-col items-center justify-center">
          <div class={twMerge('w-full', panelClass, itemClasses?.panel)}>
            {image && ( // <-- 添加图片渲染逻辑
              <div class="mb-4">
                <Image 
                  src={image.src} 
                  alt={image.alt || ''} 
                  class="rounded-lg shadow-md w-full h-auto" 
                  width={400} 
                  height={300} 
                />
              </div>
            )}
            <div class="text-center">
              {(icon || defaultIcon) && (
                <Icon
                  name={icon || defaultIcon}
                  class={twMerge('w-12 h-12 mx-auto mb-4 text-primary', defaultIconClass, itemClasses?.icon)}
                />
              )}
              {title && <h3 class={twMerge('text-2xl font-bold', titleClass, itemClasses?.title)}>{title}</h3>}
              {description && (
                <p class={twMerge('text-muted', descriptionClass, itemClasses?.description)} set:html={description} />
              )}
            </div>
          </div>
          {callToAction && (
            <div class="mt-2">
              <Button {...callToAction} />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
