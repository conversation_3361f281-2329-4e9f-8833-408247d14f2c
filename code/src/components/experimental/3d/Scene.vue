<script setup lang="ts">
import { ref, onMounted, shallowRef, type Component } from 'vue'

// Refs to hold the dynamically imported components with explicit types
const TresCanvas = shallowRef<Component | null>(null)
const OrbitControls = shallowRef<Component | null>(null)
const CameraModel = shallowRef<Component | null>(null)

// A flag to control rendering
const isReady = ref(false)

onMounted(async () => {
  // Dynamically import all 3D-related components only on the client-side
  const tresCore = await import('@tresjs/core')
  const tresCientos = await import('@tresjs/cientos')
  const modelComponent = await import('./CameraModel.vue')

  // Assign the components to our refs
  TresCanvas.value = tresCore.TresCanvas
  OrbitControls.value = tresCientos.OrbitControls
  CameraModel.value = modelComponent.default

  // All components are loaded, signal that we are ready to render
  isReady.value = true
})
</script>

<template>
  <div class="relative h-full w-full">
    <!-- 
      The 3D canvas will be mounted here.
      The UI, teleported to the target in the parent Astro page, will overlay it.
    -->
    <div v-if="!isReady" class="flex h-full w-full items-center justify-center">
      <p>Loading 3D Scene...</p>
    </div>

    <!-- 
      Only when isReady is true, render the entire 3D scene.
      We use the <component :is="..."> syntax because the component definitions 
      are now dynamic refs.
    -->

    <component v-if="isReady" :is="TresCanvas" clear-color="#F7F7F7" window-size>
      <!-- <TresPerspectiveCamera :position="[262.74, 131.14, -20.25]" /> -->
      <TresPerspectiveCamera :position="[0, 50, 250]" /> 
      <component :is="OrbitControls"  />

      <Suspense>
        <component :is="CameraModel" />
      </Suspense>

      <TresAmbientLight :intensity="1" />
      <TresDirectionalLight :position="[0, 2, 4]" :intensity="2" />
    </component>
  </div>
</template>
