
<script setup lang="ts">
import { ref, onMounted, shallowRef, nextTick, type Component } from 'vue'
import { Box } from '@tresjs/cientos'
// Refs to hold the dynamically imported components with explicit types
const TresCanvas = shallowRef<Component | null>(null)
const OrbitControls = shallowRef<Component | null>(null)
const CameraModel = shallowRef<Component | null>(null)

// A flag to control rendering
const isReady = ref(false)
const showStats = ref(false)

// Stats data
const stats = ref([
  { id: 'stat-xiaohongshu', label: '小红书粉丝', value: '10k+' },
  { id: 'stat-tiktok', label: 'TikTok 粉丝', value: '8k+' }
])

// Store the GSAP instance
let gsap: any = null

// Handle animation ready event from CameraModel
const handleAnimationReady = async (animationData: any) => {
  console.log('Animation ready event received:', animationData)
  showStats.value = true
  
  // Import GSAP if not already loaded
  if (!gsap) {
    const gsapModule = await import('gsap')
    gsap = gsapModule.default
  }

  // Wait for next tick to ensure DOM is updated
  await nextTick()
  
  // Start animations for stats
  if (gsap && animationData) {
    const { carTimeline } = animationData
    
    // Animate stats in sync with car
    stats.value.forEach((stat, index) => {
      carTimeline.to(`#${stat.id}`, {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: "elastic.out(1, 0.5)"
      }, 1.5 + index * 1)
    })
  }
}

onMounted(async () => {
  // Dynamically import all 3D-related components only on the client-side
  const tresCore = await import('@tresjs/core')
  const tresCientos = await import('@tresjs/cientos')
  const modelComponent = await import('./CameraModel.vue')

  // Assign the components to our refs
  TresCanvas.value = tresCore.TresCanvas
  OrbitControls.value = tresCientos.OrbitControls
  CameraModel.value = modelComponent.default

  // All components are loaded, signal that we are ready to render
  isReady.value = true
})
</script>

<template>
  <div class="relative h-full w-full">
    <!-- Loading message -->
    <div v-if="!isReady" class="flex h-full w-full items-center justify-center">
      <p>Loading 3D Scene...</p>
    </div>

    <!-- 3D Canvas -->
    <component v-if="isReady" :is="TresCanvas" clear-color="#F7F7F7" window-size>
      <TresPerspectiveCamera :position="[0, 50, 250]" /> 
      <component :is="OrbitControls" />

      <!-- 开发时，网格可以帮助你更好地观察空间 -->
      <TresGridHelper :args="[1000, 20]" />

      <Suspense>
        <component :is="CameraModel" @animation-ready="handleAnimationReady" />
      </Suspense>

      <!-- 
        触发区域 - 当小车进入时会触发事件的隐形盒子
        你可以将 TresMeshBasicMaterial 的 :visible 设置为 true 来调试它们的位置
      -->
      <Box :args="[40, 40, 40]" :position="[120, 10, 50]">
        <TresMeshBasicMaterial :color="0xff0000" :opacity="0.3" :transparent="true" :visible="false" />
      </Box>
      <Box :args="[40, 40, 40]" :position="[-120, 10, -50]">
        <TresMeshBasicMaterial :color="0x00ff00" :opacity="0.3" :transparent="true" :visible="false" />
      </Box>

      <TresAmbientLight :intensity="1" />
      <TresDirectionalLight :position="[0, 2, 4]" :intensity="2" />
    </component>

    <!-- 2D UI Elements - Rendered outside of TresCanvas -->
    <div
      v-if="showStats"
      class="stats-container absolute top-1/4 left-1/2 -translate-x-1/2 text-white p-4 rounded-lg z-50 w-64 text-center pointer-events-none"
    >
      <div
        v-for="stat in stats"
        :key="stat.id"
        :id="stat.id"
        class="stat mt-2 first:mt-0"
      >
        {{ stat.label }}: {{ stat.value }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.stats-container {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat {
  @apply bg-black bg-opacity-50 px-4 py-2 rounded-md text-lg font-bold;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.3s ease;
}

.stat:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
