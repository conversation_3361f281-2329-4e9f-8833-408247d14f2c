<script setup lang="ts">
import { shallowRef, onMounted, markRaw, ref, nextTick } from 'vue'

// Use shallowRef to hold the scene. This prevents deep reactivity.
const scene = shallowRef()
const isModelLoaded = ref(false)
const isAnimationReady = ref(false)

// 定义 props 来接收父组件传递的统计数据
const props = defineProps<{
  stats?: Array<{ id: string; label: string; value: string }>
}>()

// 默认统计数据
const defaultStats = [
  { id: 'stat-xiaohongshu', label: '小红书粉丝', value: '10k+' },
  { id: 'stat-tiktok', label: 'TikTok 粉丝', value: '8k+' }
]

const statsData = props.stats || defaultStats

// 动画函数
const startAnimations = (gsap: any) => {
  if (!scene.value || typeof window === 'undefined') return

  console.log('Starting animations...')

  // 启动动画
  const tl = gsap.timeline()

  // 1. Car movement animation (starts at 0s)
  tl.to(scene.value.position, {
    x: 150,
    duration: 5,
    ease: 'power2.inOut',
  }, 0)

  // 2. Animate the stats - 检查元素是否存在
  console.log('Looking for stat elements:', statsData.map(s => s.id))

  statsData.forEach((stat, index) => {
    // 确保在浏览器环境中
    if (typeof document !== 'undefined') {
      const element = document.getElementById(stat.id)
      console.log(`Element ${stat.id}:`, element)

      if (element) {
        console.log(`Animating ${stat.id}`)
        tl.to(`#${stat.id}`, {
          opacity: 1,
          scale: 1,
          duration: 0.5,
          ease: "elastic.out(1, 0.5)"
        }, 1.5 + index * 1) // 每个统计数据间隔1秒显示
      } else {
        console.warn(`Element with id ${stat.id} not found`)
      }
    }
  })
}

onMounted(async () => {
  // 确保只在客户端运行
  if (typeof window === 'undefined') return

  try {
    // 动态导入依赖，避免SSR问题
    const [{ useGLTF }, gsapModule] = await Promise.all([
      import('@tresjs/cientos'),
      import('gsap')
    ])

    const gsap = gsapModule.default

    // 加载3D模型
    const { scene: modelScene } = await useGLTF('/models/toon_car.glb')
    scene.value = markRaw(modelScene)
    isModelLoaded.value = true

    // 设置初始位置
    if (scene.value) {
      scene.value.position.x = -200
      isAnimationReady.value = true

      // 等待UI元素渲染完成后再启动动画
      await nextTick()

      // 使用 requestAnimationFrame 确保DOM完全渲染
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          startAnimations(gsap)
        })
      })
    }

  } catch (error) {
    console.error('Error loading 3D model or dependencies:', error)
  }
})
</script>

<template>
  <!-- 3D Scene will be rendered by the parent Scene.vue -->
  <primitive v-if="scene && isModelLoaded" :object="scene" :scale="0.02" />

  <!-- 2D UI Elements - 直接在组件内渲染，不使用 Teleport -->
  <div
    v-if="isAnimationReady"
    class="stats-container fixed top-1/4 left-1/2 -translate-x-1/2 text-white p-4 rounded-lg z-50 w-64 text-center pointer-events-none"
  >
    <div
      v-for="stat in statsData"
      :key="stat.id"
      :id="stat.id"
      class="stat mt-2 first:mt-0"
    >
      {{ stat.label }}: {{ stat.value }}
    </div>
  </div>
</template>

<style scoped>
.stats-container {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat {
  @apply bg-black bg-opacity-50 px-4 py-2 rounded-md text-lg font-bold;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.3s ease;
}

.stat:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
