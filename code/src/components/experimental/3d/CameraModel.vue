<script setup lang="ts">
import { shallowRef, onMounted, markRaw, ref, nextTick } from 'vue'

// Use shallowRef to hold the scene. This prevents deep reactivity.
const scene = shallowRef()
const isModelLoaded = ref(false)
const isAnimationReady = ref(false)

// Define emits
const emit = defineEmits<{
  'animation-ready': [data: { carTimeline: any }]
}>()

// 动画函数
const startAnimations = (gsap: any) => {
  if (!scene.value || typeof window === 'undefined') return

  // 创建时间线
  const tl = gsap.timeline()

  // 设置小车初始位置
  scene.value.position.x = -200

  // 小车移动动画
  tl.to(scene.value.position, {
    x: 150,
    duration: 5,
    ease: 'power2.inOut',
  }, 0)

  // 发出事件，让父组件处理2D动画
  emit('animation-ready', { carTimeline: tl })
}

onMounted(() => {
  // 确保只在客户端运行
  if (typeof window === 'undefined') return

  // 使用 setTimeout 来避免 Promise 相关的问题
  setTimeout(async () => {
    try {
      console.log('Starting to load dependencies...')

      // 动态导入依赖，避免SSR问题
      const [{ useGLTF }, gsapModule] = await Promise.all([
        import('@tresjs/cientos'),
        import('gsap')
      ])

      const gsap = gsapModule.default
      console.log('Dependencies loaded successfully')

      // 加载3D模型
      console.log('Loading 3D model...')
      const { scene: modelScene } = await useGLTF('/models/toon_car.glb')

      if (modelScene) {
        scene.value = modelScene
        isModelLoaded.value = true
        console.log('3D model loaded successfully')
        isAnimationReady.value = true

        // 等待UI元素渲染完成后再启动动画
        await nextTick()

        // 使用 requestAnimationFrame 确保DOM完全渲染
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            startAnimations(gsap)
          })
        })
      } else {
        console.error('Failed to load 3D model - scene is null')
      }

    } catch (error) {
      console.error('Error in onMounted:', error)
    }
  }, 100)
})
</script>

<template>
  <!-- Only render the 3D model, no 2D elements here -->
  <primitive v-if="scene && isModelLoaded" :object="scene" :scale="0.02" />
</template>


