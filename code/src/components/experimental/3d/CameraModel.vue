<script setup lang="ts">
import { shallowRef, onMounted, markRaw, ref, nextTick } from 'vue'
import { useGLTF } from '@tresjs/cientos'
import gsap from 'gsap'

// Use shallowRef to hold the scene. This prevents deep reactivity.
const scene = shallowRef()
const isTeleportReady = ref(false)

onMounted(async () => {
  // const { scene: modelScene } = await useGLTF('/models/Classic video camera.glb')
  const { scene: modelScene } = await useGLTF('/models/toon_car.glb')
  
  scene.value = markRaw(modelScene)

  // --- Animation Logic ---
  // Wait for the next DOM update cycle to ensure the teleport target exists.
  setTimeout(() => {
    isTeleportReady.value = true

    // Wait for another DOM update cycle to ensure the teleported content is mounted.
    nextTick(() => {
      if (scene.value) {
        // --- GSAP Timeline Animation ---
        const tl = gsap.timeline()

        // 1. Car movement animation (starts at 0s)
        tl.to(scene.value.position, {
          x: 150,
          duration: 5,
          ease: 'power2.inOut',
        }, 0)
        
        // Set initial position for the car
        scene.value.position.x = -200

        // 2. Animate the stats
        // Pop up xiaohongshu stat at 1.5s
        tl.to("#stat-xiaohongshu", { 
          opacity: 1, 
          scale: 1, 
          duration: 0.5, 
          ease: "elastic.out(1, 0.5)" 
        }, 1.5)

        // Pop up tiktok stat at 2.5s
        tl.to("#stat-tiktok", {
          opacity: 1,
          scale: 1,
          duration: 0.5,
          ease: "elastic.out(1, 0.5)"
        }, 2.5)
      }
    })
  })
})
</script>

<template>
  <Teleport to="#ui-portal-target" v-if="isTeleportReady">
    <!-- 2D UI Elements -->
    <div class="stats-container absolute top-1/4 left-1/2 -translate-x-1/2 text-white p-4 rounded-lg z-10 w-64 text-center pointer-events-auto">
      <div id="stat-xiaohongshu" class="stat">小红书粉丝: 10k+</div>
      <div id="stat-tiktok" class="stat mt-2">TikTok 粉丝: 8k+</div>
    </div>
  </Teleport>

  <!-- 3D Scene will be rendered by the parent Scene.vue -->
  <primitive v-if="scene" :object="scene" :scale="0.02" />
</template>

<style scoped>
.stat {
  @apply bg-black bg-opacity-50 px-4 py-2 rounded-md text-lg font-bold;
  opacity: 0;
  transform: scale(0.5);
}
</style>
