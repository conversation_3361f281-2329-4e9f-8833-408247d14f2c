<script setup lang="ts">
import { shallowRef, onMounted, markRaw, ref, nextTick } from 'vue'

// Use shallowRef to hold the scene. This prevents deep reactivity.
const scene = shallowRef()
const isModelLoaded = ref(false)
const isAnimationReady = ref(false)

// 定义 props 来接收父组件传递的统计数据
const props = defineProps<{
  stats?: Array<{ id: string; label: string; value: string }>
}>()

// 默认统计数据
const defaultStats = [
  { id: 'stat-xiaohongshu', label: '小红书粉丝', value: '10k+' },
  { id: 'stat-tiktok', label: 'TikTok 粉丝', value: '8k+' }
]

const statsData = props.stats || defaultStats

onMounted(async () => {
  try {
    // 动态导入依赖，避免SSR问题
    const [{ useGLTF }, gsapModule] = await Promise.all([
      import('@tresjs/cientos'),
      import('gsap')
    ])

    const gsap = gsapModule.default

    // 加载3D模型
    const { scene: modelScene } = await useGLTF('/models/toon_car.glb')
    scene.value = markRaw(modelScene)
    isModelLoaded.value = true

    // 等待下一个tick确保DOM已更新
    await nextTick()

    // 设置初始位置
    if (scene.value) {
      scene.value.position.x = -200
      isAnimationReady.value = true

      // 启动动画
      const tl = gsap.timeline()

      // 1. Car movement animation (starts at 0s)
      tl.to(scene.value.position, {
        x: 150,
        duration: 5,
        ease: 'power2.inOut',
      }, 0)

      // 2. Animate the stats
      statsData.forEach((stat, index) => {
        tl.to(`#${stat.id}`, {
          opacity: 1,
          scale: 1,
          duration: 0.5,
          ease: "elastic.out(1, 0.5)"
        }, 1.5 + index * 1) // 每个统计数据间隔1秒显示
      })
    }

  } catch (error) {
    console.error('Error loading 3D model or dependencies:', error)
  }
})
</script>

<template>
  <!-- 3D Scene will be rendered by the parent Scene.vue -->
  <primitive v-if="scene && isModelLoaded" :object="scene" :scale="0.02" />

  <!-- 2D UI Elements - 直接在组件内渲染，不使用 Teleport -->
  <div
    v-if="isAnimationReady"
    class="stats-container fixed top-1/4 left-1/2 -translate-x-1/2 text-white p-4 rounded-lg z-50 w-64 text-center pointer-events-none"
  >
    <div
      v-for="stat in statsData"
      :key="stat.id"
      :id="stat.id"
      class="stat mt-2 first:mt-0"
    >
      {{ stat.label }}: {{ stat.value }}
    </div>
  </div>
</template>

<style scoped>
.stat {
  @apply bg-black bg-opacity-50 px-4 py-2 rounded-md text-lg font-bold;
  opacity: 0;
  transform: scale(0.5);
}
</style>
