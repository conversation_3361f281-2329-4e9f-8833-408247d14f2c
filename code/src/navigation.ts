import { getPermalink, getBlogPermalink, getAsset } from './utils/permalinks';

export const headerData = {
  links: [
    {
      text: '首页',
      href: getPermalink('/'),
    },
    {
      text: '作品集',
      href: getPermalink('/portfolio'), // 假设的作品集页面路径
    },
    {
      text: '服务介绍',
      href: getPermalink('/services'),
    },
    {
      text: '关于我们',
      href: getPermalink('/about'),
    },
    {
      text: '联系我们',
      href: getPermalink('/contact'),
    },
  ],
  actions: [{ text: '开始我的定制', href: getPermalink('/start-customization'), target: '_self' }],
};

export const footerData = {
  links: [
    {
      title: '产品',
      links: [
        { text: '服务介绍', href: getPermalink('/services') },
        { text: '作品集', href: getPermalink('/portfolio') },
        { text: '开始定制', href: getPermalink('/start-customization') },
      ],
    },
    {
      title: '公司',
      links: [
        { text: '关于我们', href: getPermalink('/about') },
        { text: '联系我们', href: getPermalink('/contact') },
      ],
    },
    {
      title: '支持',
      links: [
        { text: '服务条款', href: getPermalink('/terms') },
        { text: '隐私政策', href: getPermalink('/privacy') },
      ],
    },
  ],
  secondaryLinks: [
    { text: '服务条款', href: getPermalink('/terms') },
    { text: '隐私政策', href: getPermalink('/privacy') },
  ],
  socialLinks: [
    { ariaLabel: 'Xiaohongshu', icon: 'tabler:brand-instagram', href: '#' }, // 使用 Instagram 图标作为小红书占位符
    { ariaLabel: 'TikTok', icon: 'tabler:brand-tiktok', href: '#' },
    { ariaLabel: 'Github', icon: 'tabler:brand-github', href: 'https://github.com/gtken991/gtkenProfit' },
  ],
  footNote: `
    &copy; 2024 忆帧定制 (MemoFrame Studio) · 保留所有权利.
  `,
};
