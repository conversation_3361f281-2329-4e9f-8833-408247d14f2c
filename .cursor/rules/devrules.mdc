---
description:
globs:
alwaysApply: true
---
always answer me in Chinese

# Snaps 项目核心开发准则

**目标**: 本文档旨在统一项目开发过程中的核心原则与方向，确保所有工作都围绕既定目标进行。

**最核心规则**: 尽量少的修改代码，不要大刀阔斧的修改代码，尽量维持原本的逻辑，假设必须要修改，要提示风险和具体要修改的内容（必须要解释原因，并且多方案进行评估）


## 第一部分：品牌与市场策略

### 1. 品牌定位
- **品牌名称**: 忆帧定制 (MemoFrame Studio)
- **核心卖点**: 独一无二的情感投资。
- **Slogan**: “你的故事，独一无二的艺术品。”

### 2. 核心产品线
1.  **故事动画短片**: 15-60秒动画短片。
2.  **数字叙事漫画**: 定制化风格漫画。
3.  **风格化数字肖像**: 多种艺术风格数字绘画。
4.  **宠物IP工坊**: IP形象、插画、表情包、3D模型及周边。

### 3. 品牌语调 (Tone of Voice)
- **核心基调**: 共情、温暖、富有艺术感。我们是在与用户共同创作，而不是简单地销售产品。
- **沟通原则**:
  - **使用故事性语言**: 不说“我们使用XX渲染技术”，而说“我们用光影复刻您记忆中的温度”。
  - **突出“你”而非“我”**: 文案主角是用户和他们的故事。用“即刻开启**你的**故事定制之旅”。
  - **专业且可靠**: 在介绍流程和交付时，语言要清晰、直接，给用户安全感。

### 4. 用户旅程与行动号召 (Customer Journey & CTA)
- **发现 (Discovery)**: 用户通过社交媒体（小红书/TikTok）的视觉内容（动画/漫画作品）发现我们。
- **兴趣 (Interest)**: 用户访问网站，浏览**作品集**，被案例吸引。
- **考虑 (Consideration)**: 用户查看**服务介绍**和**如何开始**页面，了解具体内容、流程和价格。
- **行动 (Action)**: 用户被清晰的CTA按钮 **“开始我的定制”** 引导，进入引导式表单完成需求提交。
- **分享 (Advocacy)**: 在交付时鼓励用户分享，扩大品牌影响力。

### 5. 内容与SEO策略
- **核心关键词**: 个人动画定制, 宠物IP设计, 情侣漫画, 照片转动画, 恋爱故事视频, 特别的纪念日礼物, 创意生日惊喜。
- **博客主题**: 策划“如何做”、“清单/合集”、“客户故事”等主题内容，吸引潜在用户并建立信任。

---


## 第二部分：开发与协作规范

### 6. 代码风格与质量
- **格式化**: 统一使用 `Prettier`。项目已配置 `.prettierrc.mjs`，请确保编辑器开启保存时自动格式化。
- **Linting**: 统一使用 `ESLint`。项目已配置 `eslint.config.mjs`，提交前应解决所有错误。


### 7. 组件开发
- **原则**: 组件应高内聚、低耦合。每个组件放在独立的文件夹中，如 `src/components/MyComponent/MyComponent.astro`。
- **Props**: 组件的Props必须定义清晰的TypeScript类型，杜绝使用 `any`。
- **样式**: 优先使用Tailwind CSS。若有组件级特定样式，在组件内部的 `<style>` 标签中编写，并添加 `is:global` 或保持局部作用域。

### 8. 资源处理（图片/视频/字体）
- **视频优化**: 所有提交到 `src/assets` 的视频都必须是经过压缩的，并根据不同设备提供不同分辨率的版本。


- **图片优化**: 所有提交到 `src/assets` 的图片都必须是经过压缩的。
- **页面图片**: 在 `.astro` 或 `.mdx` 文件中，**必须**使用Astro的 `<Image />` 组件来加载图片，以实现自动优化和懒加载。
- **图标**: 优先使用 `astro-icon` 集成，在 `astro.config.mjs` 中按需引入SVG图标。

### 9. Git 提交规范
- **Commit Message**: 强制遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范。
  - `feat`: 新功能
  - `fix`: Bug修复
  - `docs`: 文档变更
  - `style`: 代码风格调整（不影响逻辑）
  - `refactor`: 代码重构
  - `revert`: 撤销先前的提交
  - `chore`: 构建过程或辅助工具的变动
- **示例**: `feat: add custom pricing calculator component`


### 10. 无障碍访问 (Accessibility, a11y)
- **语义化HTML**: 严格使用 HTML5 语义化标签 (`<main>`, `<nav>`, `<article>`, `<section>`)。
- **图片描述**: 所有 `<Image>` 组件必须提供有意义的 `alt` 属性，对装饰性图片可设置 `alt=""`。
- **键盘导航**: 确保所有可交互元素（链接、按钮、表单）都能通过键盘清晰地聚焦和操作。
- **色彩对比度**: 确保文本和背景色的对比度符合 WCAG AA 级别标准。


## 11. 技术架构与原则

- **核心技术栈**: 项目采用现代化的前后端分离架构。
    - **前端**: **Astro**。选用理由：追求极致的加载速度、顶级的SEO表现和优秀的开发者体验。
    - **后端 (CMS)**: **Strapi**。选用理由：提供灵活的内容建模能力和对非技术人员友好的内容管理界面。
- **技术原则**:
    - **性能第一**: 网站性能是核心指标，所有技术决策需优先考虑对加载速度和用户体验的影响。

    - **关注开发者体验**: 采用的工具链应能提升开发效率，允许开发者（代称: `gtken991`）利用现有技能（如Vue.js）进行组件化开发。
    - **拥抱现代部署**: 前后端将分别部署在 Vercel/Netlify 和 Render 等现代云平台上，并利用CI/CD、Webhooks等技术实现自动化运维。



 ## 12. 开发者信息与角色

- **开发者代称**: `gtken991`
- **角色定位**:
    - **架构师与实现者**: 负责整体技术选型、框架搭建和核心功能开发。
    - **画框的制作者**: 构建网站的外部框架(Astro)，并定义其外观、交互和结构。
    - **系统的维护者**: 保证前后端系统的稳定运行和后续迭代。
- **技能利用**: 鉴于开发者有Vue经验，项目中应考虑在需要高度交互性的组件中集成Vue，以复用技能并提高效率。

## 13. 项目优先级 (第一阶段)

1.  **建立专业形象与信任**: 网站的视觉设计、性能和内容质量是最高优先级。
2.  **实现核心功能闭环**: 快速上线 MVP，包括作品展示、服务介绍和清晰的联系/咨询渠道 (特别是微信)。
3.  **内容管理便利性**: 确保非技术人员可以轻松地通过 Strapi 后台更新网站内容。
4.  **开发效率**: 选择能快速迭代的方案，尽快将产品推向市场。
期望权重：变现能力=有影响力>方便他人使用>开发难度   