# 忆帧定制 (MemoFrame Studio)

欢迎来到“忆帧定制”前端项目。本项目基于 [Astro](https://astro.build/) 构建，旨在为用户提供一个温暖、富有艺术感的平台，将他们的故事转化为独一无二的艺术品。

**Slogan**: “你的故事，独一无二的艺术品。”

## 核心技术栈

-   **前端框架**: [Astro](https://astro.build/)
-   **UI 框架**: [Tailwind CSS](https://tailwindcss.com/)
-   **内容管理**: [Strapi](https://strapi.io/) (Headless CMS)
-   **交互组件**: [Vue.js](https://vuejs.org/)

## 本地开发

所有命令都在项目根目录的终端中运行：

| 命令          | 操作                               |
| :------------ | :--------------------------------- |
| `npm install` | 安装项目依赖                       |
| `npm run dev` | 启动本地开发服务器 `localhost:4321` |
| `npm run build` | 构建生产环境版本到 `./dist/`       |
| `npm run preview` | 在本地预览生产构建的版本           |

## 开发准则

请在开发前仔细阅读 `开发文档/rules/GEMINI.md`，确保所有工作都符合品牌语调和开发规范。

